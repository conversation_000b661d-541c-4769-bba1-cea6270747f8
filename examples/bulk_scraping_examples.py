#!/usr/bin/env python3
"""
Bulk Scraping Examples for Alibaba Scraper API

This file contains Python examples for using the new bulk scraping functionality.
"""

import requests
import json
import time
from typing import List, Dict, Optional

class AlibabaBulkScraper:
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def search_products(self, query: str, max_results: int = 10, 
                       category: Optional[str] = None) -> Dict:
        """
        Search for products on Alibaba
        
        Args:
            query: Search term (e.g., "mi headphones")
            max_results: Maximum number of results (1-50)
            category: Optional category filter
            
        Returns:
            Dictionary containing search results
        """
        url = f"{self.base_url}/api/scraper/search"
        payload = {
            "query": query,
            "maxResults": max_results
        }
        
        if category:
            payload["category"] = category
            
        try:
            response = self.session.post(url, json=payload, timeout=120)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Search failed: {e}")
            return {"success": False, "error": str(e)}
    
    def bulk_scrape(self, urls: List[str], concurrent: int = 3, 
                   timeout: int = 60000, retries: int = 1) -> Dict:
        """
        Scrape multiple product URLs in parallel
        
        Args:
            urls: List of Alibaba product URLs
            concurrent: Number of parallel requests (1-5)
            timeout: Timeout per request in milliseconds
            retries: Number of retries per URL (0-3)
            
        Returns:
            Dictionary containing scraping results
        """
        url = f"{self.base_url}/api/scraper/bulk"
        payload = {
            "urls": urls,
            "options": {
                "concurrent": concurrent,
                "timeout": timeout,
                "retries": retries
            }
        }
        
        try:
            # Calculate estimated time
            estimated_time = (len(urls) / concurrent) * (timeout / 1000) * 1.5
            print(f"Estimated completion time: {estimated_time:.1f} seconds")
            
            response = self.session.post(url, json=payload, timeout=estimated_time + 60)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Bulk scraping failed: {e}")
            return {"success": False, "error": str(e)}
    
    def search_and_scrape(self, query: str, max_results: int = 10,
                         scrape_all: bool = True, **options) -> Dict:
        """
        Search for products and scrape all results in one operation
        
        Args:
            query: Search term
            max_results: Maximum number of results to find and scrape
            scrape_all: Whether to scrape all found products
            **options: Additional options for scraping (concurrent, timeout, retries)
            
        Returns:
            Dictionary containing search and scraping results
        """
        url = f"{self.base_url}/api/scraper/search-and-scrape"
        payload = {
            "query": query,
            "maxResults": max_results,
            "scrapeAll": scrape_all,
            **options
        }
        
        try:
            # This can take a long time for many products
            estimated_time = max_results * 30  # 30 seconds per product estimate
            print(f"Estimated completion time: {estimated_time:.1f} seconds")
            
            response = self.session.post(url, json=payload, timeout=estimated_time + 120)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Search and scrape failed: {e}")
            return {"success": False, "error": str(e)}
    
    def get_status(self) -> Dict:
        """Get scraper service status"""
        url = f"{self.base_url}/api/scraper/status"
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Status check failed: {e}")
            return {"success": False, "error": str(e)}


def example_search_only():
    """Example: Search for products without scraping"""
    print("=== Example 1: Search Only ===")
    
    scraper = AlibabaBulkScraper()
    
    # Search for mi headphones
    result = scraper.search_products("mi headphones", max_results=5)
    
    if result.get("success"):
        data = result["data"]
        print(f"Found {data['totalFound']} products for query: {data['query']}")
        print("Product URLs:")
        for i, url in enumerate(data.get("productUrls", []), 1):
            print(f"  {i}. {url}")
    else:
        print(f"Search failed: {result.get('error', 'Unknown error')}")


def example_bulk_scrape():
    """Example: Bulk scrape known URLs"""
    print("\n=== Example 2: Bulk Scraping ===")
    
    scraper = AlibabaBulkScraper()
    
    # First search to get some URLs
    search_result = scraper.search_products("bluetooth headphones", max_results=3)
    
    if not search_result.get("success"):
        print("Search failed, cannot proceed with bulk scraping")
        return
    
    urls = search_result["data"].get("productUrls", [])
    if not urls:
        print("No URLs found to scrape")
        return
    
    print(f"Bulk scraping {len(urls)} products...")
    
    # Bulk scrape the URLs
    result = scraper.bulk_scrape(urls, concurrent=2, retries=1)
    
    if result.get("success"):
        data = result["data"]
        summary = data["summary"]
        
        print(f"Bulk scraping completed:")
        print(f"  Total requested: {summary['totalRequested']}")
        print(f"  Successful: {summary['successful']}")
        print(f"  Failed: {summary['failed']}")
        print(f"  Success rate: {summary['successRate']}")
        print(f"  Processing time: {summary['processingTime']}")
        
        # Show successful results
        if data.get("results"):
            print("\nSuccessful scrapes:")
            for result in data["results"][:3]:  # Show first 3
                product_data = result["data"]["data"]
                print(f"  ✓ {product_data.get('title', 'No title')}")
                print(f"    Price: {product_data.get('price', 'No price')}")
                print(f"    URL: {result['url']}")
        
        # Show errors
        if data.get("errors"):
            print("\nFailed scrapes:")
            for error in data["errors"]:
                print(f"  ✗ {error['url']}")
                print(f"    Error: {error['error']}")
    else:
        print(f"Bulk scraping failed: {result.get('error', 'Unknown error')}")


def example_search_and_scrape():
    """Example: Search and scrape in one operation"""
    print("\n=== Example 3: Search and Scrape ===")
    
    scraper = AlibabaBulkScraper()
    
    print("Searching and scraping 'wireless earbuds'...")
    
    result = scraper.search_and_scrape(
        query="wireless earbuds",
        max_results=3,
        concurrent=2,
        timeout=90000,
        retries=1
    )
    
    if result.get("success"):
        data = result["data"]
        
        # Search results
        search_results = data["searchResults"]
        print(f"Search found {search_results['totalFound']} products")
        
        # Scraping results
        if data.get("scrapingResults"):
            scraping_results = data["scrapingResults"]
            summary = scraping_results["summary"]
            
            print(f"Scraping completed:")
            print(f"  Success rate: {summary['successRate']}")
            print(f"  Processing time: {summary['processingTime']}")
            
            # Show products
            if scraping_results.get("results"):
                print("\nScraped products:")
                for result in scraping_results["results"]:
                    product_data = result["data"]["data"]
                    print(f"  • {product_data.get('title', 'No title')}")
                    print(f"    Price: {product_data.get('price', 'No price')}")
                    print(f"    Supplier: {product_data.get('supplier', 'No supplier')}")
        
        # Overall summary
        if data.get("summary"):
            overall = data["summary"]
            print(f"\nOverall Summary:")
            print(f"  Products found: {overall['searchFound']}")
            print(f"  Scraping attempted: {overall['scrapingAttempted']}")
            print(f"  Scraping successful: {overall['scrapingSuccessful']}")
            print(f"  Overall success rate: {overall['overallSuccessRate']}")
    else:
        print(f"Search and scrape failed: {result.get('error', 'Unknown error')}")


def example_with_error_handling():
    """Example: Comprehensive error handling"""
    print("\n=== Example 4: Error Handling ===")
    
    scraper = AlibabaBulkScraper()
    
    # Check service status first
    status = scraper.get_status()
    if not status.get("success"):
        print("Service is not available")
        return
    
    print("Service is operational")
    
    # Try scraping with some invalid URLs mixed in
    test_urls = [
        "https://www.alibaba.com/product-detail/valid-url-1",  # This will likely fail
        "https://www.alibaba.com/product-detail/valid-url-2",  # This will likely fail
        "invalid-url"  # This will definitely fail validation
    ]
    
    print("Testing error handling with invalid URLs...")
    
    result = scraper.bulk_scrape(test_urls, concurrent=1, retries=0)
    
    if result.get("success"):
        data = result["data"]
        print(f"Results: {data['summary']['successful']} successful, {data['summary']['failed']} failed")
        
        if data.get("errors"):
            print("Error details:")
            for error in data["errors"]:
                print(f"  URL: {error['url']}")
                print(f"  Error: {error['error']}")
                print(f"  Type: {error.get('errorType', 'Unknown')}")
    else:
        print(f"Operation failed: {result.get('error')}")


if __name__ == "__main__":
    print("Alibaba Bulk Scraping Examples")
    print("=" * 40)
    
    # Run all examples
    try:
        example_search_only()
        example_bulk_scrape()
        example_search_and_scrape()
        example_with_error_handling()
        
        print("\n" + "=" * 40)
        print("All examples completed!")
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
