#!/usr/bin/env node
/**
 * Simple Phone Search Example
 * 
 * This example shows how to search for "phones" on Alibaba and get multiple product results.
 * It demonstrates the new bulk scraping functionality with CAPTCHA bypass.
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function searchPhones() {
  console.log('🔍 Searching for phones on Alibaba...\n');
  
  try {
    // Method 1: Simple search for phones
    console.log('Method 1: Simple Product Search');
    console.log('================================');
    
    const searchResponse = await axios.post(`${BASE_URL}/api/scraper/search`, {
      query: 'phones',
      maxResults: 5
    }, {
      timeout: 120000 // 2 minutes timeout
    });

    if (searchResponse.data.success) {
      const searchData = searchResponse.data.data;
      console.log(`✅ Found ${searchData.totalFound} phone products!`);
      console.log(`📋 Product URLs:`);
      
      searchData.productUrls.forEach((url, index) => {
        console.log(`   ${index + 1}. ${url}`);
      });
      
      console.log(`\n⏱️  Search completed in: ${searchResponse.data.metadata.processingTime}`);
    } else {
      console.log('❌ Search failed:', searchResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Search error:', error.response?.data?.message || error.message);
  }
}

async function searchAndScrapePhones() {
  console.log('\n\n🔍 Searching and scraping phones...\n');
  
  try {
    // Method 2: Search and scrape all results
    console.log('Method 2: Search and Scrape All Results');
    console.log('======================================');
    
    const response = await axios.post(`${BASE_URL}/api/scraper/search-and-scrape`, {
      query: 'phones',
      maxResults: 3, // Start small to avoid long wait times
      concurrent: 2,  // Process 2 products at a time
      timeout: 90000, // 90 seconds per product
      retries: 1
    }, {
      timeout: 600000 // 10 minutes total timeout
    });

    if (response.data.success) {
      const data = response.data.data;
      
      console.log(`📊 Search Results:`);
      console.log(`   Found: ${data.searchResults.totalFound} products`);
      console.log(`   Attempted to scrape: ${data.scrapingResults?.summary.totalRequested || 0}`);
      console.log(`   Successfully scraped: ${data.scrapingResults?.summary.successful || 0}`);
      console.log(`   Success rate: ${data.scrapingResults?.summary.successRate || '0%'}`);
      
      if (data.scrapingResults?.results) {
        console.log(`\n📱 Phone Products Found:`);
        console.log('========================');
        
        data.scrapingResults.results.forEach((result, index) => {
          const product = result.data.data;
          console.log(`\n${index + 1}. ${product.title || 'No title'}`);
          console.log(`   💰 Price: ${product.price || 'No price'}`);
          console.log(`   🏢 Supplier: ${product.supplier || 'No supplier'}`);
          console.log(`   🔗 URL: ${result.url}`);
        });
      }
      
      if (data.scrapingResults?.errors && data.scrapingResults.errors.length > 0) {
        console.log(`\n❌ Failed Products:`);
        data.scrapingResults.errors.forEach((error, index) => {
          console.log(`   ${index + 1}. ${error.url}`);
          console.log(`      Error: ${error.error}`);
        });
      }
      
      console.log(`\n⏱️  Total processing time: ${response.data.metadata.processingTime}`);
    } else {
      console.log('❌ Search and scrape failed:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Search and scrape error:', error.response?.data?.message || error.message);
  }
}

async function searchWithCustomUrl() {
  console.log('\n\n🔍 Using custom search URL...\n');
  
  try {
    // Method 3: Use a specific search URL
    console.log('Method 3: Custom Search URL');
    console.log('===========================');
    
    const customUrl = 'https://www.alibaba.com/trade/search?spm=a2700.product_home_newuser.home_new_user_first_screen_fy23_pc_search_bar.keydown__Enter&tab=all&SearchText=phones';
    
    const response = await axios.post(`${BASE_URL}/api/scraper/search-url`, {
      searchUrl: customUrl,
      maxResults: 3,
      scrapeAll: false // Just get URLs, don't scrape products
    }, {
      timeout: 120000
    });

    if (response.data.success) {
      const data = response.data.data;
      console.log(`✅ Found ${data.totalFound} products from custom URL`);
      console.log(`📋 Product URLs:`);
      
      data.productUrls.forEach((url, index) => {
        console.log(`   ${index + 1}. ${url}`);
      });
    } else {
      console.log('❌ Custom URL search failed:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Custom URL error:', error.response?.data?.message || error.message);
  }
}

async function checkServerStatus() {
  try {
    const response = await axios.get(`${BASE_URL}/api/scraper/status`);
    if (response.data.success) {
      console.log('✅ Server is running and operational');
      return true;
    } else {
      console.log('❌ Server is not operational');
      return false;
    }
  } catch (error) {
    console.log('❌ Cannot connect to server. Make sure it\'s running on port 3000');
    console.log('   Run: npm start');
    return false;
  }
}

async function main() {
  console.log('📱 Alibaba Phone Search Demo');
  console.log('============================\n');
  
  // Check if server is running
  const serverOk = await checkServerStatus();
  if (!serverOk) {
    process.exit(1);
  }
  
  console.log('🚀 Starting phone search examples...\n');
  
  // Run examples
  await searchPhones();
  await searchAndScrapePhones();
  await searchWithCustomUrl();
  
  console.log('\n✨ Demo completed!');
  console.log('\n💡 Tips:');
  console.log('   - Start with small maxResults (3-5) to test');
  console.log('   - Increase concurrent processing gradually');
  console.log('   - Monitor success rates and adjust timeouts');
  console.log('   - Use search-only first, then scrape specific products');
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Demo interrupted by user');
  process.exit(0);
});

// Run the demo
if (require.main === module) {
  main().catch(error => {
    console.error('\n💥 Demo failed:', error.message);
    process.exit(1);
  });
}

module.exports = {
  searchPhones,
  searchAndScrapePhones,
  searchWithCustomUrl,
  checkServerStatus
};
