{"name": "scrapping-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.6.1", "crewai-js": "^0.0.1", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["crewai", "scraping", "alibaba", "api", "nodejs"], "author": "", "license": "ISC", "description": ""}