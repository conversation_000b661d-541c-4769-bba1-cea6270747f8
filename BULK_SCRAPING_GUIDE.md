# Bulk Scraping Guide

This guide explains how to use the new bulk scraping functionality to search and scrape multiple products from Alibaba with advanced CAPTCHA bypass capabilities.

## 🚀 Quick Start

**Want to search for "phones" and get multiple product data?** Here's how:

```bash
# Search and scrape phones in one command
curl -X POST http://localhost:3000/api/scraper/search-and-scrape \
  -H "Content-Type: application/json" \
  -d '{
    "query": "phones",
    "maxResults": 5,
    "concurrent": 2
  }'
```

This will:
1. Search Alibaba for "phones"
2. Find 5 product URLs
3. Scrape each product's details
4. Return complete product information

## New Features

### 1. Smart Product Search
Search for products using simple keywords like "phones", "headphones", "laptops" with intelligent CAPTCHA bypass.

### 2. Multiple Search Strategies
Automatically tries different search URL patterns if one gets blocked by CAPTCHA.

### 3. Bulk Scraping
Scrape multiple product URLs in parallel with configurable concurrency and retry logic.

### 4. Search and Scrape
Combine search and scraping in one operation - search for products and automatically scrape all results.

### 5. Custom Search URLs
Use your own Alibaba search URLs if you have specific requirements.

## API Endpoints

### 1. Search Products
**Endpoint**: `POST /api/scraper/search`

Search for products on Alibaba using a query string.

**Request Body**:
```json
{
  "query": "mi headphones",
  "maxResults": 10,
  "category": "Electronics",
  "priceRange": {
    "min": 10,
    "max": 100
  }
}
```

**Parameters**:
- `query` (required): Search term (2-100 characters)
- `maxResults` (optional): Maximum number of results (1-50, default: 10)
- `category` (optional): Product category filter
- `priceRange` (optional): Price range filter with min/max values

**Response**:
```json
{
  "success": true,
  "data": {
    "query": "mi headphones",
    "searchUrl": "https://www.alibaba.com/trade/search?...",
    "totalFound": 8,
    "productUrls": [
      "https://www.alibaba.com/product-detail/...",
      "https://www.alibaba.com/product-detail/..."
    ],
    "searchedAt": "2024-01-15T10:30:00.000Z"
  },
  "metadata": {
    "query": "mi headphones",
    "searchedAt": "2024-01-15T10:30:00.000Z",
    "processingTime": "3500ms"
  }
}
```

### 2. Bulk Scraping
**Endpoint**: `POST /api/scraper/bulk`

Scrape multiple product URLs in parallel.

**Request Body**:
```json
{
  "urls": [
    "https://www.alibaba.com/product-detail/...",
    "https://www.alibaba.com/product-detail/..."
  ],
  "options": {
    "concurrent": 3,
    "timeout": 60000,
    "retries": 1
  }
}
```

**Parameters**:
- `urls` (required): Array of Alibaba product URLs (1-20 URLs)
- `options` (optional): Configuration object
  - `concurrent`: Number of parallel requests (1-5, default: 3)
  - `timeout`: Timeout per request in ms (30000-300000, default: 60000)
  - `retries`: Number of retries per URL (0-3, default: 1)

**Response**:
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalRequested": 5,
      "successful": 4,
      "failed": 1,
      "successRate": "80.00%",
      "processingTime": "45000ms",
      "completedAt": "2024-01-15T10:35:00.000Z"
    },
    "results": [
      {
        "index": 0,
        "url": "https://www.alibaba.com/product-detail/...",
        "success": true,
        "data": {
          "title": "Product Title",
          "price": "$10-50",
          "supplier": "Company Name"
        },
        "scrapedAt": "2024-01-15T10:32:00.000Z"
      }
    ],
    "errors": [
      {
        "index": 4,
        "url": "https://www.alibaba.com/product-detail/...",
        "success": false,
        "error": "Page not found",
        "errorType": "PAGE_NOT_FOUND",
        "attemptedAt": "2024-01-15T10:34:00.000Z"
      }
    ]
  }
}
```

### 3. Search and Scrape
**Endpoint**: `POST /api/scraper/search-and-scrape`

Search for products and automatically scrape all results in one operation.

### 4. Custom Search URL
**Endpoint**: `POST /api/scraper/search-url`

Use your own Alibaba search URL to extract and scrape products.

**Request Body**:
```json
{
  "searchUrl": "https://www.alibaba.com/trade/search?SearchText=phones",
  "maxResults": 10,
  "scrapeAll": true,
  "concurrent": 3,
  "timeout": 90000,
  "retries": 1
}
```

**Parameters**:
- `searchUrl` (required): Your Alibaba search URL
- `maxResults` (optional): Maximum products to find (default: 10)
- `scrapeAll` (optional): Whether to scrape all found products (default: true)
- `concurrent` (optional): Parallel scraping (1-5, default: 3)
- `timeout` (optional): Timeout per product in ms (default: 60000)
- `retries` (optional): Retries per product (0-3, default: 1)

**Response** (when scrapeAll=true):
```json
{
  "success": true,
  "data": {
    "query": "phones",
    "searchResults": {
      "totalFound": 8,
      "productUrls": ["..."]
    },
    "scrapingResults": {
      "summary": {
        "totalRequested": 8,
        "successful": 7,
        "failed": 1,
        "successRate": "87.50%"
      },
      "results": [
        {
          "url": "https://www.alibaba.com/product-detail/...",
          "success": true,
          "data": {
            "data": {
              "title": "iPhone 15 Pro Max",
              "price": "$800-1200",
              "supplier": "Tech Company Ltd"
            }
          }
        }
      ]
    }
  }
}
  "query": "mi headphones",
  "maxResults": 5,
  "scrapeAll": true,
  "concurrent": 2,
  "timeout": 60000,
  "retries": 1
}
```

**Parameters**:
- All search parameters from `/search` endpoint
- All bulk options from `/bulk` endpoint
- `scrapeAll` (optional): Whether to scrape all found products (default: true)

**Response**:
```json
{
  "success": true,
  "data": {
    "query": "mi headphones",
    "searchResults": {
      "totalFound": 5,
      "productUrls": ["..."]
    },
    "scrapingResults": {
      "summary": {
        "totalRequested": 5,
        "successful": 4,
        "failed": 1,
        "successRate": "80.00%"
      },
      "results": [...],
      "errors": [...]
    },
    "summary": {
      "searchFound": 5,
      "scrapingAttempted": 5,
      "scrapingSuccessful": 4,
      "overallSuccessRate": "80.00%"
    }
  }
}
```

## Usage Examples

### Example 1: Search for Products Only
```bash
curl -X POST http://localhost:3000/api/scraper/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mi headphones",
    "maxResults": 10
  }'
```

### Example 2: Bulk Scrape Known URLs
```bash
curl -X POST http://localhost:3000/api/scraper/bulk \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://www.alibaba.com/product-detail/url1",
      "https://www.alibaba.com/product-detail/url2"
    ],
    "options": {
      "concurrent": 2,
      "retries": 1
    }
  }'
```

### Example 3: Search and Scrape Everything
```bash
curl -X POST http://localhost:3000/api/scraper/search-and-scrape \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mi headphones",
    "maxResults": 5,
    "concurrent": 2
  }'
```

## JavaScript/Node.js Examples

### Search and Scrape with Axios
```javascript
const axios = require('axios');

async function searchAndScrapeProducts(query, maxResults = 10) {
  try {
    const response = await axios.post('http://localhost:3000/api/scraper/search-and-scrape', {
      query: query,
      maxResults: maxResults,
      concurrent: 3,
      retries: 1
    });

    const data = response.data.data;
    console.log(`Found ${data.searchResults.totalFound} products`);
    console.log(`Successfully scraped ${data.scrapingResults.summary.successful} products`);

    // Process results
    data.scrapingResults.results.forEach((result, index) => {
      console.log(`Product ${index + 1}:`);
      console.log(`  Title: ${result.data.data.title}`);
      console.log(`  Price: ${result.data.data.price}`);
      console.log(`  URL: ${result.url}`);
    });

    return response.data;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Usage
searchAndScrapeProducts('mi headphones', 5);
```

### Bulk Scraping with Error Handling
```javascript
async function bulkScrapeProducts(urls) {
  try {
    const response = await axios.post('http://localhost:3000/api/scraper/bulk', {
      urls: urls,
      options: {
        concurrent: 3,
        timeout: 90000,
        retries: 2
      }
    });

    const data = response.data.data;
    console.log(`Bulk scraping completed: ${data.summary.successRate} success rate`);

    // Handle successful results
    if (data.results) {
      data.results.forEach(result => {
        console.log(`✓ ${result.data.data.title}`);
      });
    }

    // Handle errors
    if (data.errors) {
      console.log('\nFailed URLs:');
      data.errors.forEach(error => {
        console.log(`✗ ${error.url}: ${error.error}`);
      });
    }

    return response.data;
  } catch (error) {
    console.error('Bulk scraping failed:', error.response?.data || error.message);
  }
}
```

## Performance Considerations

### Concurrency Limits
- Maximum 5 concurrent requests to avoid overwhelming Alibaba's servers
- Default is 3 concurrent requests for optimal balance

### Rate Limiting
- Built-in delays between batches (2-5 seconds)
- Exponential backoff for retries
- Respects existing rate limits (100 requests per 15 minutes)

### Timeout Settings
- Individual request timeout: 30-300 seconds (default: 60 seconds)
- Total operation may take much longer for large batches

### Memory Usage
- Results are stored in memory during processing
- Consider memory limits for very large batches (>20 URLs)

## Error Handling

### Common Error Types
- `TIMEOUT_ERROR`: Request took too long
- `PAGE_NOT_FOUND`: Product page doesn't exist
- `ACCESS_DENIED`: Blocked by Alibaba
- `NETWORK_ERROR`: Connection issues
- `CAPTCHA_REQUIRED`: Human verification needed

### Partial Success
- Bulk operations continue even if some URLs fail
- Detailed error information provided for failed requests
- Success rate calculated and reported

## Best Practices

1. **Start Small**: Test with 2-3 URLs before scaling up
2. **Monitor Success Rates**: Adjust concurrency if success rate drops
3. **Handle Errors Gracefully**: Always check for partial failures
4. **Respect Rate Limits**: Don't exceed recommended request rates
5. **Use Appropriate Timeouts**: Longer timeouts for complex products
6. **Implement Retry Logic**: Use built-in retry functionality
