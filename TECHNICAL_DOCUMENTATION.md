# Technical Documentation

## Overview

This document provides a detailed technical explanation of how the Alibaba Product Scraper Backend works, including code architecture, data flow, and implementation details.

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client        │───▶│   Express API    │───▶│   Scraper       │
│                 │    │                  │    │   Service       │
│ • HTTP Requests │    │ • Validation     │    │                 │
│ • JSON Payload  │    │ • Rate Limiting  │    │ • Puppeteer     │
│ • E<PERSON>r Handling│    │ • <PERSON><PERSON><PERSON> Handling │    │ • CrewAI        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Middleware     │    │   Browser       │
                       │                  │    │                 │
                       │ • Validation     │    │ • Page Loading  │
                       │ • Error Handler  │    │ • Content       │
                       │ • Logger         │    │   Extraction    │
                       └──────────────────┘    └─────────────────┘
```

### Component Breakdown

#### 1. Express Server (`src/server.js`)

**Purpose**: Main application entry point and HTTP server setup.

**Key Features**:
- Security middleware (Helmet, CORS)
- Rate limiting with express-rate-limit
- Body parsing with size limits
- Health check endpoint
- Graceful shutdown handling

**Code Structure**:
```javascript
// Security setup
app.use(helmet());
app.use(cors());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

// Routes
app.use('/api/scraper', scraperRoutes);
```

#### 2. Scraper Service (`src/services/scraperService.js`)

**Purpose**: Core scraping logic with hybrid AI/traditional approach.

**Key Components**:

##### Browser Management
- Puppeteer browser instance management
- Anti-bot detection measures
- Multiple navigation strategies
- Automatic retry mechanisms

##### CrewAI Integration
- AI agent initialization
- Task definition for data extraction
- Intelligent data processing
- Fallback to basic extraction

##### Data Processing Pipeline
1. URL validation
2. Browser automation
3. Content extraction
4. AI processing (if available)
5. Basic extraction (fallback)
6. Response formatting

**Code Flow**:
```javascript
async scrapeAlibabaProduct(url) {
  // 1. Validate URL
  if (!this.isValidAlibabaUrl(url)) {
    throw new Error('Invalid Alibaba URL');
  }

  // 2. Get page content with Puppeteer
  const pageContent = await this.getPageContent(url);

  // 3. Check for OpenAI API key
  const hasValidApiKey = process.env.OPENAI_API_KEY && 
                        process.env.OPENAI_API_KEY.length > 10;

  // 4. Process with CrewAI or fallback to basic
  if (hasValidApiKey) {
    return await this.processWithCrewAI(pageContent, url);
  } else {
    return await this.basicDataExtraction(pageContent, url);
  }
}
```

#### 3. Middleware Layer

##### Validation Middleware (`src/middleware/validation.js`)
- Joi schema validation
- URL format checking
- Alibaba domain verification

##### Error Handler (`src/middleware/errorHandler.js`)
- Centralized error processing
- Error categorization
- HTTP status code mapping
- Development vs production error details

#### 4. Utilities

##### Logger (`src/utils/logger.js`)
- Winston-based structured logging
- Multiple transport configuration
- Environment-based log levels
- File rotation and size management

## Data Flow

### 1. Request Processing

```
Client Request
    ↓
Rate Limiting Check
    ↓
URL Validation (Joi + Custom)
    ↓
Scraper Service Invocation
    ↓
Browser Automation (Puppeteer)
    ↓
Content Extraction
    ↓
AI Processing (CrewAI) OR Basic Extraction
    ↓
Response Formatting
    ↓
Client Response
```

### 2. Error Handling Flow

```
Error Occurs
    ↓
Error Categorization
    ↓
Retry Logic (if applicable)
    ↓
Error Enhancement (type, category, suggestion)
    ↓
HTTP Status Code Assignment
    ↓
Structured Error Response
    ↓
Client Error Response
```

## Processing Modes

### 1. CrewAI Mode (AI-Powered)

**Initialization**:
```javascript
initializeCrew() {
  // Define specialized agents
  this.scraperAgent = new Agent({
    name: 'Web Scraper Specialist',
    role: 'Web Scraper Specialist',
    goal: 'Extract comprehensive product information',
    backstory: 'Expert web scraper with e-commerce knowledge',
    llm: 'gpt-3.5-turbo'
  });

  this.analyzerAgent = new Agent({
    name: 'Data Analyzer',
    role: 'Data Analyzer', 
    goal: 'Clean, structure, and validate scraped data',
    backstory: 'Data analyst specializing in e-commerce data',
    llm: 'gpt-3.5-turbo'
  });

  // Define tasks
  this.scrapeTask = new Task({
    description: 'Extract all relevant product information...',
    agent: this.scraperAgent
  });

  this.analyzeTask = new Task({
    description: 'Clean and structure the scraped data...',
    agent: this.analyzerAgent
  });

  // Create crew
  this.scrapingCrew = new Crew({
    agents: [this.scraperAgent, this.analyzerAgent],
    tasks: [this.scrapeTask, this.analyzeTask]
  });
}
```

**Processing**:
1. Update agent goals with specific URL and content
2. Execute crew to process content
3. Structure results into standardized format
4. Return enhanced data with AI insights

### 2. Basic Mode (Rule-Based)

**Implementation**:
```javascript
async basicDataExtraction(pageContent, url) {
  const cheerio = require('cheerio');
  const $ = cheerio.load(pageContent.html);

  // Extract using CSS selectors
  const title = $('title').text() || $('h1').first().text();
  
  // Price extraction with multiple selectors
  const priceSelectors = ['.price', '.product-price', '[class*="price"]'];
  let price = 'Price not found';
  for (const selector of priceSelectors) {
    const priceText = $(selector).first().text().trim();
    if (priceText) {
      price = priceText;
      break;
    }
  }

  // Similar logic for description, supplier, images
  return structuredData;
}
```

### 3. Minimal Mode (Fallback)

**Purpose**: Emergency fallback when all other methods fail.

**Returns**:
- Page title
- Basic error message
- Raw HTML content (first 500 characters)
- Debugging information

## Browser Automation Details

### Anti-Bot Detection Measures

```javascript
// Remove webdriver property
await page.evaluateOnNewDocument(() => {
  Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined,
  });
});

// Set realistic user agent
await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64)...');

// Randomized viewport
const viewportWidth = 1366 + Math.floor(Math.random() * 200);
await page.setViewport({ width: viewportWidth, height: viewportHeight });

// Additional headers
await page.setExtraHTTPHeaders({
  'Accept': 'text/html,application/xhtml+xml...',
  'Accept-Language': 'en-US,en;q=0.9',
  'DNT': '1'
});
```

### Navigation Strategies

The scraper uses multiple fallback navigation strategies:

1. **networkidle0**: Wait until no network requests for 500ms
2. **domcontentloaded**: Wait for DOM to be loaded
3. **load**: Wait for basic page load event

```javascript
// Strategy 1: Try networkidle0 first
try {
  await page.goto(url, { waitUntil: 'networkidle0', timeout });
} catch (error) {
  // Strategy 2: Try domcontentloaded
  try {
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout });
  } catch (error2) {
    // Strategy 3: Try basic load
    await page.goto(url, { waitUntil: 'load', timeout });
  }
}
```

### Retry Mechanism

```javascript
async getPageContent(url, retryCount = 0) {
  const maxRetries = 3;
  const baseTimeout = 60000;
  const timeout = baseTimeout + (retryCount * 15000); // Increase timeout

  try {
    // Attempt page loading
    return await this.loadPage(url, timeout);
  } catch (error) {
    if (retryCount < maxRetries) {
      // Wait before retry with exponential backoff
      await new Promise(resolve => 
        setTimeout(resolve, (retryCount + 1) * 2000)
      );
      return this.getPageContent(url, retryCount + 1);
    }
    throw error;
  }
}
```

## Error Handling System

### Error Categorization

```javascript
categorizeError(error) {
  const message = error.message.toLowerCase();

  if (message.includes('timeout')) {
    return {
      type: 'TIMEOUT_ERROR',
      category: 'Network',
      suggestion: 'Check network or increase timeout'
    };
  }

  if (message.includes('403') || message.includes('forbidden')) {
    return {
      type: 'ACCESS_DENIED', 
      category: 'Authorization',
      suggestion: 'Website may be blocking requests'
    };
  }

  // Additional categorization logic...
}
```

### HTTP Status Code Mapping

```javascript
// In routes/scraper.js
let statusCode = 500; // Default
if (error.type === 'PAGE_NOT_FOUND') {
  statusCode = 404;
} else if (error.type === 'ACCESS_DENIED') {
  statusCode = 403;
} else if (error.type === 'TIMEOUT_ERROR') {
  statusCode = 408;
}
```

## Performance Considerations

### Memory Management
- Browser instances are properly closed after use
- Page objects are cleaned up in finally blocks
- Single-process browser flag reduces memory usage

### Timeout Management
- Progressive timeout increases with retries
- Different timeouts for different operations
- Configurable base timeout via environment variables

### Concurrency
- Single browser instance reused across requests
- Page-level isolation for concurrent requests
- Proper cleanup prevents memory leaks

## Security Features

### Input Validation
- Joi schema validation for request structure
- URL format validation
- Domain restriction to Alibaba.com

### Rate Limiting
- IP-based rate limiting
- Configurable windows and limits
- Standard HTTP 429 responses

### Security Headers
- Helmet.js for security headers
- CORS configuration
- Content Security Policy

## Monitoring and Observability

### Structured Logging
```javascript
logger.info('Scraping completed successfully', {
  url: url,
  processingTime: duration,
  processingMethod: 'crewai'
});
```

### Health Checks
- Service status endpoint
- Browser readiness check
- CrewAI initialization status

### Error Tracking
- Categorized error logging
- Performance metrics
- Success/failure rates

This technical documentation provides the foundation for understanding, maintaining, and extending the scraper system.
