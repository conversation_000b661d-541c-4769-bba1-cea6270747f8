# Deployment Guide

## Overview

This guide covers different deployment options for the Alibaba Product Scraper Backend, from local development to production environments.

## Prerequisites

- Node.js 16+ 
- npm or yarn
- Chrome/Chromium browser
- OpenAI API key (optional, for CrewAI mode)

## Local Development

### 1. Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd scrapping-backend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Start development server
npm run dev
```

### 2. Environment Configuration

Edit `.env` file:
```bash
# Required
PORT=3000
NODE_ENV=development

# Optional (for AI mode)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Puppeteer settings
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=60000

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
```

### 3. Verify Installation

```bash
# Check health endpoint
curl http://localhost:3000/health

# Test scraping
curl -X POST http://localhost:3000/api/scraper/alibaba \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.alibaba.com/product-detail/test"}'
```

## Production Deployment

### Option 1: Traditional Server (Ubuntu/CentOS)

#### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Chrome dependencies
sudo apt-get install -y \
  gconf-service libasound2 libatk1.0-0 libcairo2 libcups2 \
  libfontconfig1 libgdk-pixbuf2.0-0 libgtk-3-0 libnspr4 \
  libpango-1.0-0 libxss1 fonts-liberation libappindicator1 \
  libnss3 lsb-release xdg-utils wget

# Install PM2 for process management
sudo npm install -g pm2
```

#### 2. Application Deployment

```bash
# Create application directory
sudo mkdir -p /opt/scrapper-backend
cd /opt/scrapper-backend

# Clone and setup
git clone <repository-url> .
npm install --production

# Create environment file
sudo cp .env.example .env
sudo nano .env
```

#### 3. PM2 Configuration

Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'scraper-backend',
    script: 'src/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true
  }]
};
```

#### 4. Start Application

```bash
# Start with PM2
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
sudo env PATH=$PATH:/usr/bin pm2 startup systemd -u $USER --hp $HOME
```

#### 5. Nginx Reverse Proxy

Install and configure Nginx:
```bash
sudo apt install nginx

# Create Nginx configuration
sudo nano /etc/nginx/sites-available/scraper-backend
```

Nginx configuration:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
}
```

Enable site:
```bash
sudo ln -s /etc/nginx/sites-available/scraper-backend /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Option 2: Docker Deployment

#### 1. Create Dockerfile

```dockerfile
FROM node:18-alpine

# Install Chrome dependencies
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Set Chrome path
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Expose port
EXPOSE 3000

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S scraper -u 1001
USER scraper

# Start application
CMD ["node", "src/server.js"]
```

#### 2. Create docker-compose.yml

```yaml
version: '3.8'

services:
  scraper-backend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - PUPPETEER_HEADLESS=true
      - PUPPETEER_TIMEOUT=60000
      - LOG_LEVEL=info
    env_file:
      - .env
    volumes:
      - ./logs:/usr/src/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - scraper-backend
    restart: unless-stopped
```

#### 3. Build and Deploy

```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f scraper-backend

# Scale if needed
docker-compose up -d --scale scraper-backend=3
```

### Option 3: Cloud Deployment (AWS/GCP/Azure)

#### AWS EC2 Deployment

1. **Launch EC2 Instance**:
   - Choose Ubuntu 20.04 LTS
   - Instance type: t3.medium or larger
   - Security group: Allow HTTP (80), HTTPS (443), SSH (22)

2. **Setup Application**:
   ```bash
   # Connect to instance
   ssh -i your-key.pem ubuntu@your-instance-ip
   
   # Follow traditional server setup steps above
   ```

3. **Load Balancer** (for multiple instances):
   - Create Application Load Balancer
   - Configure health checks to `/health`
   - Set up auto-scaling group

#### Google Cloud Run

1. **Create Dockerfile** (as above)

2. **Deploy to Cloud Run**:
   ```bash
   # Build and push to Container Registry
   gcloud builds submit --tag gcr.io/PROJECT-ID/scraper-backend
   
   # Deploy to Cloud Run
   gcloud run deploy scraper-backend \
     --image gcr.io/PROJECT-ID/scraper-backend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --memory 2Gi \
     --cpu 2 \
     --timeout 300s \
     --max-instances 10
   ```

## Environment Variables for Production

```bash
# Server
NODE_ENV=production
PORT=3000

# OpenAI (if using CrewAI)
OPENAI_API_KEY=sk-your-production-api-key

# Puppeteer
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=90000

# Rate Limiting (adjust for production load)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=500

# Logging
LOG_LEVEL=warn

# Security (if needed)
HELMET_CONTENT_SECURITY_POLICY=false
CORS_ORIGIN=https://your-frontend-domain.com
```

## Monitoring and Maintenance

### 1. Health Monitoring

Setup monitoring for:
- `/health` endpoint
- Memory usage
- CPU usage
- Response times
- Error rates

### 2. Log Management

```bash
# Rotate logs with logrotate
sudo nano /etc/logrotate.d/scraper-backend
```

Logrotate configuration:
```
/opt/scraper-backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 scraper scraper
    postrotate
        pm2 reload scraper-backend
    endscript
}
```

### 3. Backup Strategy

```bash
# Backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backup/scraper-backend-$DATE.tar.gz \
  /opt/scraper-backend \
  --exclude=node_modules \
  --exclude=logs
```

### 4. Updates and Maintenance

```bash
# Update application
cd /opt/scraper-backend
git pull origin main
npm install --production
pm2 reload scraper-backend

# Update system packages
sudo apt update && sudo apt upgrade -y
```

## Security Considerations

1. **Firewall Configuration**:
   ```bash
   sudo ufw allow ssh
   sudo ufw allow 80
   sudo ufw allow 443
   sudo ufw enable
   ```

2. **SSL Certificate** (Let's Encrypt):
   ```bash
   sudo apt install certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

3. **Environment Security**:
   - Store sensitive variables in secure vaults
   - Use IAM roles for cloud deployments
   - Regular security updates

## Troubleshooting

### Common Issues

1. **Chrome/Puppeteer Issues**:
   ```bash
   # Install missing dependencies
   sudo apt-get install -y gconf-service libasound2 libatk1.0-0
   ```

2. **Memory Issues**:
   - Increase server memory
   - Monitor browser instances
   - Implement connection pooling

3. **Permission Issues**:
   ```bash
   # Fix file permissions
   sudo chown -R scraper:scraper /opt/scraper-backend
   sudo chmod -R 755 /opt/scraper-backend
   ```

4. **Port Conflicts**:
   ```bash
   # Check port usage
   sudo netstat -tulpn | grep :3000
   ```

## Performance Optimization

1. **Clustering**: Use PM2 cluster mode
2. **Caching**: Implement Redis for response caching
3. **Load Balancing**: Use Nginx or cloud load balancers
4. **Resource Limits**: Set appropriate memory/CPU limits
5. **Connection Pooling**: Reuse browser instances

This deployment guide provides comprehensive instructions for various deployment scenarios. Choose the option that best fits your infrastructure and requirements.
