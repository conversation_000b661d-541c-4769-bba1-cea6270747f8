# API Documentation

## Base Information

- **Base URL**: `http://localhost:3000`
- **Content-Type**: `application/json`
- **Authentication**: None required
- **Rate Limiting**: 100 requests per 15 minutes per IP

## Endpoints

### 1. Scrape Alibaba Product

**Endpoint**: `POST /api/scraper/alibaba`

**Description**: Scrapes product information from an Alibaba product page using hybrid AI/traditional extraction methods.

#### Request

**Headers**:
```
Content-Type: application/json
```

**Body**:
```json
{
  "url": "https://www.alibaba.com/product-detail/..."
}
```

**Parameters**:
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `url` | string | Yes | Valid Alibaba.com product URL |

#### Response

**Success Response (200)**:
```json
{
  "success": true,
  "data": {
    "url": "https://www.alibaba.com/product-detail/16W-Color-Change-Led-Fiber-Optic_62031547683.html",
    "scrapedAt": "2024-01-15T10:30:00.000Z",
    "source": "alibaba",
    "processingMethod": "crewai",
    "data": {
      "title": "16W Color Change LED Fiber Optic Light Kit",
      "description": "High-quality LED fiber optic lighting system with color changing capabilities...",
      "price": "$10.50 - $25.00 / piece",
      "supplier": "Shenzhen Tech Co., Ltd",
      "images": [
        "https://s.alicdn.com/@sc04/kf/H123456789.jpg",
        "https://s.alicdn.com/@sc04/kf/H987654321.jpg"
      ],
      "specifications": {
        "power": "16W",
        "voltage": "12V DC",
        "material": "Plastic + PMMA"
      }
    },
    "pageInfo": {
      "finalUrl": "https://www.alibaba.com/product-detail/...",
      "readyState": "complete",
      "extractedAt": "2024-01-15T10:30:00.000Z"
    }
  },
  "metadata": {
    "url": "https://www.alibaba.com/product-detail/...",
    "scrapedAt": "2024-01-15T10:30:00.000Z",
    "processingTime": "45000ms"
  }
}
```

**Error Response (400 - Validation Error)**:
```json
{
  "success": false,
  "error": "Validation failed",
  "message": "Please provide a valid Alibaba.com URL",
  "field": "url"
}
```

**Error Response (408 - Timeout)**:
```json
{
  "success": false,
  "error": "Failed to scrape product data",
  "message": "Navigation timeout of 60000ms exceeded",
  "errorType": "TIMEOUT_ERROR",
  "category": "Network",
  "suggestion": "The page took too long to load. This might be due to slow network or the page being blocked.",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Error Response (403 - Access Denied)**:
```json
{
  "success": false,
  "error": "Failed to scrape product data",
  "message": "Access denied by target website",
  "errorType": "ACCESS_DENIED",
  "category": "Authorization",
  "suggestion": "The website might be blocking automated requests. Try again later or use a different approach.",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Error Response (429 - Rate Limited)**:
```json
{
  "success": false,
  "error": "Too many requests from this IP, please try again later."
}
```

#### Processing Methods

The response includes a `processingMethod` field indicating how the data was extracted:

- **`crewai`**: AI-powered extraction using CrewAI agents
- **`basic`**: Rule-based extraction using CSS selectors
- **`minimal`**: Fallback mode returning basic page information

### 2. Get Scraper Status

**Endpoint**: `GET /api/scraper/status`

**Description**: Returns the current status of the scraper service.

#### Request

No parameters required.

#### Response

**Success Response (200)**:
```json
{
  "success": true,
  "status": {
    "service": "ScraperService",
    "status": "operational",
    "crewInitialized": true,
    "browserReady": false,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

**Error Response (500)**:
```json
{
  "success": false,
  "error": "Failed to get service status"
}
```

### 3. Health Check

**Endpoint**: `GET /health`

**Description**: Basic health check endpoint for monitoring.

#### Request

No parameters required.

#### Response

**Success Response (200)**:
```json
{
  "status": "OK",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "environment": "development"
}
```

## HTTP Status Codes

| Code | Description | When It Occurs |
|------|-------------|----------------|
| 200 | OK | Successful request |
| 400 | Bad Request | Invalid request format or validation error |
| 403 | Forbidden | Access denied by target website |
| 404 | Not Found | Invalid endpoint or page not found |
| 408 | Request Timeout | Page loading timeout |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Unexpected server error |
| 503 | Service Unavailable | Network connectivity issues |

## Error Types

| Error Type | Description | Suggested Action |
|------------|-------------|------------------|
| `TIMEOUT_ERROR` | Page loading timeout | Check network, increase timeout |
| `NETWORK_ERROR` | Connectivity issues | Check internet connection |
| `ACCESS_DENIED` | Blocked by website | Wait and retry, check IP blocking |
| `PAGE_NOT_FOUND` | Invalid URL | Verify URL is correct |
| `CAPTCHA_REQUIRED` | Human verification needed | Manual intervention required |
| `UNKNOWN_ERROR` | Unexpected error | Contact support |

## Usage Examples

### cURL Examples

**Basic scraping request**:
```bash
curl -X POST http://localhost:3000/api/scraper/alibaba \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.alibaba.com/product-detail/16W-Color-Change-Led-Fiber-Optic_62031547683.html"
  }'
```

**Check service status**:
```bash
curl http://localhost:3000/api/scraper/status
```

**Health check**:
```bash
curl http://localhost:3000/health
```

### JavaScript/Node.js Examples

**Using axios**:
```javascript
const axios = require('axios');

async function scrapeProduct(url) {
  try {
    const response = await axios.post('http://localhost:3000/api/scraper/alibaba', {
      url: url
    });
    
    const data = response.data.data;
    console.log(`Title: ${data.data.title}`);
    console.log(`Price: ${data.data.price}`);
    console.log(`Processing: ${data.processingMethod}`);
    
    return response.data;
  } catch (error) {
    if (error.response) {
      console.error('Error:', error.response.data);
    } else {
      console.error('Network error:', error.message);
    }
  }
}
```

**Using fetch**:
```javascript
async function scrapeProduct(url) {
  try {
    const response = await fetch('http://localhost:3000/api/scraper/alibaba', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url: url })
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Request failed');
    }
    
    return data;
  } catch (error) {
    console.error('Scraping failed:', error.message);
  }
}
```

### Python Examples

**Using requests**:
```python
import requests
import json

def scrape_product(url):
    try:
        response = requests.post(
            'http://localhost:3000/api/scraper/alibaba',
            json={'url': url},
            timeout=120  # 2 minutes timeout
        )
        response.raise_for_status()
        
        data = response.json()
        print(f"Title: {data['data']['data']['title']}")
        print(f"Processing: {data['data']['processingMethod']}")
        
        return data
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response: {e.response.text}")

# Usage
result = scrape_product('https://www.alibaba.com/product-detail/...')
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Window**: 15 minutes (900,000 ms)
- **Limit**: 100 requests per IP per window
- **Headers**: Standard rate limit headers are included in responses
- **Response**: HTTP 429 when limit exceeded

**Rate limit headers**:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

## Best Practices

1. **Handle Errors Gracefully**: Always check the `success` field and handle different error types appropriately.

2. **Respect Rate Limits**: Implement client-side rate limiting to avoid hitting the API limits.

3. **Use Appropriate Timeouts**: Set reasonable timeouts for your HTTP requests (recommended: 2+ minutes).

4. **Monitor Processing Methods**: Check the `processingMethod` to understand data quality expectations.

5. **Implement Retry Logic**: For timeout and network errors, implement exponential backoff retry logic.

6. **Cache Results**: Consider caching successful responses to reduce API calls.

## Support

For API issues:
1. Check the error message and type for specific guidance
2. Verify your request format matches the documentation
3. Test with the health endpoint to ensure service availability
4. Review rate limiting if receiving 429 errors
