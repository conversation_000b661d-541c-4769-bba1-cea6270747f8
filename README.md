# Alibaba Product Scraper Backend

A sophisticated Node.js backend service for scraping product information from Alibaba.com using a hybrid approach combining Puppeteer for browser automation and CrewAI for intelligent data extraction.

## 🚀 Features

- **Hybrid Scraping Architecture**: Combines Puppeteer (browser automation) + CrewAI (AI-powered data extraction)
- **Bulk Scraping**: Search and scrape multiple products in parallel with configurable concurrency
- **Product Search**: Find products on Alibaba using keywords and filters
- **Search and Scrape**: Combined operation to search and automatically scrape all results
- **Three Processing Modes**: CrewAI (AI-powered), Basic (rule-based), and Minimal (fallback)
- **Robust Error Handling**: Categorized errors with specific suggestions and retry mechanisms
- **Anti-Bot Detection**: Advanced stealth measures to avoid detection
- **Rate Limiting**: Built-in protection against abuse
- **Comprehensive Logging**: Structured logging with Winston
- **Input Validation**: Joi-based request validation
- **Health Monitoring**: Service status endpoints

## 📋 Table of Contents

- [Architecture Overview](#architecture-overview)
- [How It Works](#how-it-works)
- [Installation](#installation)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [Usage Examples](#usage-examples)
- [Processing Modes](#processing-modes)
- [Error Handling](#error-handling)
- [Monitoring](#monitoring)
- [Development](#development)
- [Troubleshooting](#troubleshooting)

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Express API   │───▶│    Puppeteer     │───▶│    CrewAI       │
│                 │    │                  │    │                 │
│ • Rate Limiting │    │ • Browser        │    │ • AI Agents     │
│ • Validation    │    │ • Navigation     │    │ • Data Analysis │
│ • Error Handler │    │ • Anti-bot       │    │ • Structuring   │
│ • Logging       │    │ • Content Extract│    │ • Validation    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Core Components

1. **Express Server** (`src/server.js`)
   - Security middleware (Helmet, CORS)
   - Rate limiting
   - Request validation
   - Error handling

2. **Scraper Service** (`src/services/scraperService.js`)
   - Puppeteer browser management
   - CrewAI integration
   - Multi-mode processing
   - Error categorization

3. **Middleware Layer**
   - Input validation (`src/middleware/validation.js`)
   - Error handling (`src/middleware/errorHandler.js`)

4. **Utilities**
   - Structured logging (`src/utils/logger.js`)

## 🔧 How It Works

### 1. Request Flow

```
Client Request → Validation → Scraper Service → Browser Automation → AI Processing → Response
```

### 2. Processing Pipeline

1. **URL Validation**: Ensures valid Alibaba.com URLs
2. **Browser Launch**: Puppeteer with anti-detection measures
3. **Page Navigation**: Multiple fallback strategies
4. **Content Extraction**: Raw HTML and metadata
5. **AI Processing**: CrewAI agents analyze and structure data
6. **Fallback Handling**: Basic extraction if AI fails
7. **Response Formatting**: Structured JSON output

### 3. Three-Tier Processing

#### 🤖 CrewAI Mode (AI-Powered)
- **When**: Valid OpenAI API key configured
- **Process**: AI agents extract and structure data intelligently
- **Benefits**: High accuracy, adapts to page variations
- **Speed**: Slower (60-90s) but highest quality

#### 🔧 Basic Mode (Rule-Based)
- **When**: No OpenAI API key or CrewAI fails
- **Process**: Cheerio selectors extract common elements
- **Benefits**: Fast, reliable, no API costs
- **Speed**: Fast (5-10s) with medium accuracy

#### 🚨 Minimal Mode (Fallback)
- **When**: Basic extraction fails
- **Process**: Returns raw content only
- **Benefits**: Always works, debugging info
- **Speed**: Fastest (1s) with basic data

## 📦 Installation

### Prerequisites

- Node.js 16+
- npm or yarn
- Chrome/Chromium (for Puppeteer)

### Setup

```bash
# Clone the repository
git clone <repository-url>
cd scrapping-backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Configure environment variables
nano .env
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the root directory:

```bash
# Server Configuration
PORT=3000
NODE_ENV=development

# OpenAI API (required for CrewAI AI mode)
# CrewAI uses OpenAI's GPT models for intelligent data extraction
OPENAI_API_KEY=sk-your-openai-api-key-here

# Puppeteer Configuration
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=60000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
```

### Required Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PORT` | Server port | 3000 | No |
| `OPENAI_API_KEY` | OpenAI API key (CrewAI uses GPT models) | - | For AI mode |
| `PUPPETEER_HEADLESS` | Run browser headless | true | No |
| `PUPPETEER_TIMEOUT` | Page load timeout (ms) | 60000 | No |

### Important Note About API Keys

**Why OpenAI API Key (not CrewAI API Key)?**

CrewAI is an orchestration framework that uses OpenAI's GPT models under the hood. When you see:
```javascript
llm: 'gpt-3.5-turbo'  // In the agent configuration
```

This means CrewAI will call OpenAI's API to process the data. So you need:
- ✅ **OpenAI API Key** (for the actual AI processing)
- ❌ **No separate CrewAI API key needed** (CrewAI is just the framework)

**Cost**: You'll be charged by OpenAI for GPT API usage, not by CrewAI.

## 📚 API Documentation

### Base URL
```
http://localhost:3000
```

### Endpoints

#### POST /api/scraper/alibaba
Scrape a single Alibaba product page.

#### POST /api/scraper/search
Search for products on Alibaba using keywords.

#### POST /api/scraper/bulk
Scrape multiple Alibaba product URLs in parallel.

#### POST /api/scraper/search-and-scrape
Search for products and automatically scrape all results.

### Single Product Scraping

#### POST /api/scraper/alibaba
Scrape an Alibaba product page.

**Request:**
```json
{
  "url": "https://www.alibaba.com/product-detail/..."
}
```

**Response (Success):**
```json
{
  "success": true,
  "data": {
    "url": "https://www.alibaba.com/product-detail/...",
    "scrapedAt": "2024-01-15T10:30:00.000Z",
    "source": "alibaba",
    "processingMethod": "crewai",
    "data": {
      "title": "Product Title",
      "description": "Product description...",
      "price": "$10.00 - $50.00",
      "supplier": "Company Name",
      "images": ["url1", "url2"]
    },
    "pageInfo": {
      "finalUrl": "https://...",
      "readyState": "complete",
      "extractedAt": "2024-01-15T10:30:00.000Z"
    }
  },
  "metadata": {
    "url": "https://...",
    "scrapedAt": "2024-01-15T10:30:00.000Z",
    "processingTime": "45000ms"
  }
}
```

**Response (Error):**
```json
{
  "success": false,
  "error": "Failed to scrape product data",
  "message": "Navigation timeout exceeded",
  "errorType": "TIMEOUT_ERROR",
  "category": "Network",
  "suggestion": "The page took too long to load...",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

#### GET /api/scraper/status
Get scraper service status.

**Response:**
```json
{
  "success": true,
  "status": {
    "service": "ScraperService",
    "status": "operational",
    "crewInitialized": true,
    "browserReady": false,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

#### GET /health
Health check endpoint.

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "environment": "development"
}
```

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request (validation error) |
| 403 | Access Denied (blocked by target site) |
| 404 | Page Not Found |
| 408 | Request Timeout |
| 429 | Too Many Requests (rate limited) |
| 500 | Internal Server Error |
| 503 | Service Unavailable (network error) |

## 💡 Usage Examples

### Basic Usage

```bash
# Start the server
npm start

# Scrape a product (using curl)
curl -X POST http://localhost:3000/api/scraper/alibaba \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.alibaba.com/product-detail/16W-Color-Change-Led-Fiber-Optic_62031547683.html"}'
```

### Using with JavaScript/Node.js

```javascript
const axios = require('axios');

async function scrapeProduct(url) {
  try {
    const response = await axios.post('http://localhost:3000/api/scraper/alibaba', {
      url: url
    });

    console.log('Processing method:', response.data.data.processingMethod);
    console.log('Product title:', response.data.data.data.title);
    console.log('Price:', response.data.data.data.price);

    return response.data;
  } catch (error) {
    console.error('Scraping failed:', error.response?.data || error.message);
  }
}

// Usage
scrapeProduct('https://www.alibaba.com/product-detail/...');
```

### Using with Python

```python
import requests

def scrape_product(url):
    try:
        response = requests.post(
            'http://localhost:3000/api/scraper/alibaba',
            json={'url': url}
        )
        response.raise_for_status()

        data = response.json()
        print(f"Processing method: {data['data']['processingMethod']}")
        print(f"Product title: {data['data']['data']['title']}")

        return data
    except requests.exceptions.RequestException as e:
        print(f"Scraping failed: {e}")

# Usage
scrape_product('https://www.alibaba.com/product-detail/...')
```

### Bulk Scraping Examples

#### Search for Products
```bash
# Search for "mi headphones" and get up to 10 product URLs
curl -X POST http://localhost:3000/api/scraper/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mi headphones",
    "maxResults": 10
  }'
```

#### Bulk Scrape Multiple URLs
```bash
# Scrape multiple products in parallel
curl -X POST http://localhost:3000/api/scraper/bulk \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://www.alibaba.com/product-detail/url1",
      "https://www.alibaba.com/product-detail/url2"
    ],
    "options": {
      "concurrent": 3,
      "retries": 1
    }
  }'
```

#### Search and Scrape Everything
```bash
# Search for products and scrape all results in one operation
curl -X POST http://localhost:3000/api/scraper/search-and-scrape \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mi headphones",
    "maxResults": 5,
    "concurrent": 2
  }'
```

#### JavaScript Example: Search and Scrape
```javascript
const axios = require('axios');

async function searchAndScrapeProducts(query, maxResults = 10) {
  try {
    const response = await axios.post('http://localhost:3000/api/scraper/search-and-scrape', {
      query: query,
      maxResults: maxResults,
      concurrent: 3,
      retries: 1
    });

    const data = response.data.data;
    console.log(`Found ${data.searchResults.totalFound} products`);
    console.log(`Successfully scraped ${data.scrapingResults.summary.successful} products`);

    // Process results
    data.scrapingResults.results.forEach((result, index) => {
      console.log(`Product ${index + 1}:`);
      console.log(`  Title: ${result.data.data.title}`);
      console.log(`  Price: ${result.data.data.price}`);
      console.log(`  URL: ${result.url}`);
    });

    return response.data;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Usage
searchAndScrapeProducts('mi headphones', 5);
```

For more comprehensive examples, see:
- [`BULK_SCRAPING_GUIDE.md`](./BULK_SCRAPING_GUIDE.md) - Complete guide with all endpoints
- [`examples/bulk_scraping_examples.py`](./examples/bulk_scraping_examples.py) - Python examples

## 🎯 Processing Modes

### CrewAI Mode (Recommended)

**Requirements:**
- Valid OpenAI API key
- Internet connection

**Capabilities:**
- Intelligent data extraction
- Adapts to different page layouts
- Cleans and structures data
- Handles complex product specifications

**Example Output:**
```json
{
  "processingMethod": "crewai",
  "data": {
    "title": "16W Color Change LED Fiber Optic Light",
    "description": "High-quality LED fiber optic lighting system...",
    "price": {
      "min": 10.50,
      "max": 25.00,
      "currency": "USD",
      "moq": 100
    },
    "supplier": {
      "name": "Shenzhen Tech Co., Ltd",
      "location": "Guangdong, China",
      "rating": 4.5
    },
    "specifications": {
      "power": "16W",
      "voltage": "12V DC",
      "color": "RGB Color Changing"
    }
  }
}
```

### Basic Mode

**When Used:**
- No OpenAI API key configured
- CrewAI processing fails
- Fast processing needed

**Capabilities:**
- CSS selector-based extraction
- Basic data cleaning
- Image URL extraction
- Fast processing

**Example Output:**
```json
{
  "processingMethod": "basic",
  "data": {
    "title": "16W Color Change Led Fiber Optic Light",
    "description": "Product description extracted from page...",
    "price": "$10.50 - $25.00 / piece",
    "supplier": "Shenzhen Tech Co., Ltd",
    "images": [
      "https://s.alicdn.com/@sc04/kf/image1.jpg",
      "https://s.alicdn.com/@sc04/kf/image2.jpg"
    ]
  }
}
```

### Minimal Mode

**When Used:**
- All other modes fail
- Debugging page structure
- Emergency fallback

**Output:**
```json
{
  "processingMethod": "minimal",
  "data": {
    "title": "Page title from HTML",
    "description": "Minimal extraction completed",
    "rawContent": "First 500 characters of page HTML..."
  }
}
```

## 🚨 Error Handling

### Error Categories

| Type | Description | HTTP Code | Suggestion |
|------|-------------|-----------|------------|
| `TIMEOUT_ERROR` | Page load timeout | 408 | Check network, increase timeout |
| `NETWORK_ERROR` | Connectivity issues | 503 | Check internet connection |
| `ACCESS_DENIED` | Blocked by website | 403 | Try again later, site may be blocking |
| `PAGE_NOT_FOUND` | Invalid URL | 404 | Verify the URL is correct |
| `CAPTCHA_REQUIRED` | Human verification | 403 | Manual intervention needed |
| `UNKNOWN_ERROR` | Unexpected error | 500 | Contact support |

### Retry Mechanism

The scraper automatically retries failed requests:
- **Max retries**: 3 attempts
- **Timeout increase**: +15s per retry
- **Backoff delay**: 2s, 4s, 6s between retries

### Error Response Format

```json
{
  "success": false,
  "error": "Failed to scrape product data",
  "message": "Navigation timeout of 60000ms exceeded",
  "errorType": "TIMEOUT_ERROR",
  "category": "Network",
  "suggestion": "The page took too long to load. This might be due to slow network or the page being blocked.",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 📊 Monitoring

### Logging

The application uses Winston for structured logging:

**Log Levels:**
- `error`: Critical failures
- `warn`: Retries and recoverable issues
- `info`: General operation info (default)
- `debug`: Detailed debugging information

**Log Files:**
- `logs/error.log`: Error-level logs only
- `logs/combined.log`: All log levels

**Log Format:**
```json
{
  "timestamp": "2024-01-15 10:30:00",
  "level": "info",
  "message": "Scraping completed successfully",
  "service": "crewai-scraper"
}
```

### Health Monitoring

Monitor these key metrics:

1. **Success Rate**: Percentage of successful scrapes
2. **Response Time**: Average processing time per request
3. **Error Distribution**: Types and frequency of errors
4. **Memory Usage**: Browser and Node.js memory consumption
5. **Rate Limit Hits**: Number of rate-limited requests

### Status Endpoint

Use `/health` and `/api/scraper/status` for monitoring:

```bash
# Health check
curl http://localhost:3000/health

# Service status
curl http://localhost:3000/api/scraper/status
```

## 🛠️ Development

### Running in Development

```bash
# Install dependencies
npm install

# Start with auto-reload
npm run dev

# Run tests
npm test

# Start in production mode
npm start
```

### Project Structure

```
scrapping-backend/
├── src/
│   ├── middleware/
│   │   ├── errorHandler.js    # Global error handling
│   │   └── validation.js      # Request validation
│   ├── routes/
│   │   └── scraper.js         # API routes
│   ├── services/
│   │   └── scraperService.js  # Core scraping logic
│   ├── utils/
│   │   └── logger.js          # Logging configuration
│   └── server.js              # Express app setup
├── logs/                      # Log files
├── package.json
└── README.md
```

### Key Dependencies

| Package | Purpose | Version |
|---------|---------|---------|
| `express` | Web framework | ^4.18.2 |
| `puppeteer` | Browser automation | ^21.6.1 |
| `crewai-js` | AI-powered data extraction | ^0.0.1 |
| `cheerio` | HTML parsing | ^1.0.0-rc.12 |
| `winston` | Logging | ^3.11.0 |
| `joi` | Input validation | ^17.11.0 |
| `helmet` | Security middleware | ^7.1.0 |

### Testing

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Test specific endpoint
curl -X POST http://localhost:3000/api/scraper/alibaba \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.alibaba.com/product-detail/test"}'
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Timeout Errors
**Symptoms:** Requests failing with `TIMEOUT_ERROR`
**Solutions:**
- Increase `PUPPETEER_TIMEOUT` in `.env`
- Check network connectivity
- Verify target URL is accessible

#### 2. Access Denied
**Symptoms:** `ACCESS_DENIED` errors
**Solutions:**
- Wait before retrying (rate limiting)
- Check if IP is blocked
- Verify URL is correct

#### 3. CrewAI Not Working
**Symptoms:** Always falls back to basic mode
**Solutions:**
- Verify `OPENAI_API_KEY` is set correctly
- Check OpenAI API quota/billing
- Ensure API key has proper permissions

#### 4. High Memory Usage
**Symptoms:** Server running out of memory
**Solutions:**
- Browser instances are cleaned up automatically
- Restart service periodically
- Monitor with `docker stats` if containerized

#### 5. Rate Limiting
**Symptoms:** 429 Too Many Requests
**Solutions:**
- Reduce request frequency
- Adjust `RATE_LIMIT_MAX_REQUESTS` in `.env`
- Implement client-side rate limiting

### Debug Mode

Enable debug logging:

```bash
# Set log level to debug
LOG_LEVEL=debug npm start

# Run browser in non-headless mode
PUPPETEER_HEADLESS=false npm start
```

### Performance Optimization

1. **Browser Management**
   - Reuse browser instances
   - Close unused pages promptly
   - Use `--single-process` flag

2. **Memory Management**
   - Monitor memory usage
   - Implement page pooling
   - Regular garbage collection

3. **Network Optimization**
   - Block unnecessary resources (images, CSS)
   - Use connection pooling
   - Implement caching layer

### Support

For issues and questions:

1. Check the logs in `logs/` directory
2. Verify configuration in `.env`
3. Test with the `/health` endpoint
4. Review error messages for specific suggestions

## 📄 License

This project is licensed under the ISC License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

**Built with ❤️ using Node.js, Puppeteer, and CrewAI**
