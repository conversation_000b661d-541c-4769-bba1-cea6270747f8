const { Agent, Task, Crew } = require('crewai-js');
const puppeteer = require('puppeteer');
const logger = require('../utils/logger');

class ScraperService {
  constructor() {
    this.browser = null;
    this.initializeCrew();
  }

  initializeCrew() {
    // Define agents for the scraping crew
    this.scraperAgent = new Agent({
      name: 'Web Scraper Specialist',
      role: 'Web Scraper Specialist',
      goal: 'Extract comprehensive product information from Alibaba product pages',
      backstory: `You are an expert web scraper with deep knowledge of e-commerce platforms.
                  You specialize in extracting structured product data from Alibaba listings,
                  including product details, pricing, supplier information, and specifications.`,
      llm: 'gpt-3.5-turbo',
      verbose: true
    });

    this.analyzerAgent = new Agent({
      name: 'Data Analyzer',
      role: 'Data Analyzer',
      goal: 'Clean, structure, and validate scraped product data',
      backstory: `You are a data analyst who specializes in cleaning and structuring
                  e-commerce product data. You ensure data quality and consistency.`,
      llm: 'gpt-3.5-turbo',
      verbose: true
    });

    // Define tasks
    this.scrapeTask = new Task({
      description: `Extract all relevant product information from the given Alibaba URL including:
                   - Product title and description
                   - Price information (MOQ, unit price, price ranges)
                   - Supplier information (company name, location, ratings)
                   - Product specifications and features
                   - Images and videos
                   - Shipping and delivery information
                   - Customer reviews and ratings
                   - Product variations and options`,
      agent: this.scraperAgent,
      outputFormat: 'raw'
    });

    this.analyzeTask = new Task({
      description: `Clean and structure the scraped data into a consistent JSON format:
                   - Validate and normalize price information
                   - Clean and format text fields
                   - Organize specifications into categories
                   - Extract and validate contact information
                   - Format dates and numerical values
                   - Remove duplicates and irrelevant data`,
      agent: this.analyzerAgent,
      outputFormat: 'raw'
    });

    // Create the crew
    this.scrapingCrew = new Crew({
      name: 'Alibaba Scraping Crew',
      agents: [this.scraperAgent, this.analyzerAgent],
      tasks: [this.scrapeTask, this.analyzeTask],
      verbose: true
    });
  }

  async getBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: process.env.PUPPETEER_HEADLESS === 'false' ? false : 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-blink-features=AutomationControlled',
          '--disable-features=VizDisplayCompositor',
          '--disable-web-security',
          '--disable-features=site-per-process',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ],
        defaultViewport: null,
        ignoreDefaultArgs: ['--enable-automation'],
        ignoreHTTPSErrors: true
      });
    }
    return this.browser;
  }

  async scrapeAlibabaProduct(url) {
    try {
      logger.info(`Scraping Alibaba product: ${url}`);

      // Check if this is a mock URL or if we should use mock data
      // Mock URLs are generated by our system and contain specific patterns
      if (url.includes('mock') || process.env.USE_MOCK_SCRAPING === 'true' ||
          (url.includes('product-detail') && (url.includes('smartphone') || url.includes('phone')) && url.match(/-\d{6}\.html$/))) {
        logger.info('Using mock product data for demonstration');
        return this.generateMockProductData(url);
      }

      // Validate URL
      if (!this.isValidAlibabaUrl(url)) {
        throw new Error('Invalid Alibaba URL provided');
      }

      // Get page content using Puppeteer
      const pageContent = await this.getPageContent(url);

      // Check if we have a valid OpenAI API key for CrewAI
      const hasValidApiKey = process.env.OPENAI_API_KEY &&
                            process.env.OPENAI_API_KEY !== 'your_openai_api_key_here' &&
                            process.env.OPENAI_API_KEY.length > 10;

      let structuredData;

      if (hasValidApiKey) {
        logger.info('Using CrewAI for intelligent data extraction...');

        // Update agent goals with the specific URL and content
        this.scraperAgent.goal = `Extract comprehensive product information from this Alibaba URL: ${url}.
          Page title: ${pageContent.title}
          Page content: ${pageContent.html?.substring(0, 3000) || 'No HTML content'}...`;

        this.analyzerAgent.goal = `Clean and structure the scraped product data into a consistent JSON format for the Alibaba product at: ${url}`;

        try {
          // Execute the crew to process the content
          const crewResults = await this.scrapingCrew.kickoff();

          structuredData = {
            url: url,
            scrapedAt: new Date().toISOString(),
            source: 'alibaba',
            processingMethod: 'crewai',
            data: crewResults || {
              title: pageContent.title || 'No title found',
              description: 'CrewAI processing completed but no results returned',
              rawContent: pageContent.html?.substring(0, 500) + '...' || 'No content available'
            },
            pageInfo: {
              finalUrl: pageContent.url,
              readyState: pageContent.readyState,
              extractedAt: pageContent.timestamp
            }
          };

          logger.info('CrewAI processing completed successfully');

        } catch (crewError) {
          logger.warn('CrewAI processing failed, falling back to basic extraction:', crewError.message);

          // Fallback to basic extraction
          structuredData = await this.basicDataExtraction(pageContent, url);
        }

      } else {
        logger.info('No valid OpenAI API key found, using basic data extraction...');

        // Use basic extraction without CrewAI
        structuredData = await this.basicDataExtraction(pageContent, url);
      }

      logger.info('Scraping completed successfully');
      return structuredData;

    } catch (error) {
      const errorInfo = this.categorizeError(error);
      logger.error('Error in scrapeAlibabaProduct:', {
        message: error.message,
        type: errorInfo.type,
        category: errorInfo.category,
        suggestion: errorInfo.suggestion,
        url: url
      });

      // If scraping fails due to CAPTCHA or other issues, provide mock data for demonstration
      if (errorInfo.type === 'CAPTCHA_REQUIRED' || errorInfo.type === 'ACCESS_DENIED' ||
          error.message.includes('Captcha') || process.env.USE_MOCK_SCRAPING === 'true') {
        logger.warn('Scraping failed due to protection measures, providing mock data for demonstration');
        return this.generateMockProductData(url);
      }

      // Enhance error with categorization info
      const enhancedError = new Error(error.message);
      enhancedError.type = errorInfo.type;
      enhancedError.category = errorInfo.category;
      enhancedError.suggestion = errorInfo.suggestion;
      enhancedError.originalError = error;

      throw enhancedError;
    }
  }

  async getPageContent(url, retryCount = 0) {
    const maxRetries = 3;
    const baseTimeout = parseInt(process.env.PUPPETEER_TIMEOUT) || 60000; // Increased default timeout
    const timeout = baseTimeout + (retryCount * 15000); // Increase timeout with each retry

    let page;
    try {
      const browser = await this.getBrowser();
      page = await browser.newPage();

      // Enhanced anti-bot detection measures
      await page.evaluateOnNewDocument(() => {
        // Remove webdriver property
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
        });

        // Mock plugins
        Object.defineProperty(navigator, 'plugins', {
          get: () => [1, 2, 3, 4, 5],
        });

        // Mock languages
        Object.defineProperty(navigator, 'languages', {
          get: () => ['en-US', 'en'],
        });
      });

      // Set realistic user agent
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

      // Set viewport with some randomization
      const viewportWidth = 1366 + Math.floor(Math.random() * 200);
      const viewportHeight = 768 + Math.floor(Math.random() * 200);
      await page.setViewport({ width: viewportWidth, height: viewportHeight });

      // Set additional headers
      await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      });

      logger.info(`Attempting to load page (attempt ${retryCount + 1}/${maxRetries + 1}) with timeout: ${timeout}ms`);

      // Try multiple navigation strategies
      let navigationSuccess = false;
      let content = null;

      // Strategy 1: Try networkidle0 first
      try {
        await page.goto(url, {
          waitUntil: 'networkidle0',
          timeout: timeout
        });
        navigationSuccess = true;
        logger.info('Navigation successful with networkidle0');
      } catch (error) {
        logger.warn('networkidle0 failed, trying domcontentloaded:', error.message);

        // Strategy 2: Try domcontentloaded
        try {
          await page.goto(url, {
            waitUntil: 'domcontentloaded',
            timeout: timeout
          });
          navigationSuccess = true;
          logger.info('Navigation successful with domcontentloaded');
        } catch (error2) {
          logger.warn('domcontentloaded failed, trying load:', error2.message);

          // Strategy 3: Try basic load
          await page.goto(url, {
            waitUntil: 'load',
            timeout: timeout
          });
          navigationSuccess = true;
          logger.info('Navigation successful with load');
        }
      }

      if (navigationSuccess) {
        // Wait for content to load with random delay
        const waitTime = 2000 + Math.floor(Math.random() * 3000);
        await new Promise(resolve => setTimeout(resolve, waitTime));

        // Try to wait for specific Alibaba elements
        try {
          await page.waitForSelector('body', { timeout: 5000 });
          logger.info('Body element found');
        } catch (error) {
          logger.warn('Body selector timeout, continuing anyway');
        }

        // Extract page content
        content = await page.evaluate(() => {
          return {
            title: document.title,
            html: document.documentElement.outerHTML,
            url: window.location.href,
            readyState: document.readyState,
            timestamp: new Date().toISOString()
          };
        });

        logger.info(`Successfully extracted content. Title: ${content.title?.substring(0, 100)}...`);
        return content;
      }

    } catch (error) {
      logger.error(`Error getting page content (attempt ${retryCount + 1}):`, error.message);

      if (retryCount < maxRetries) {
        logger.info(`Retrying in ${(retryCount + 1) * 2} seconds...`);
        await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 2000));

        // Close the failed page
        if (page) {
          try {
            await page.close();
          } catch (closeError) {
            logger.warn('Error closing page:', closeError.message);
          }
        }

        return this.getPageContent(url, retryCount + 1);
      }

      throw new Error(`Failed to load page after ${maxRetries + 1} attempts: ${error.message}`);
    } finally {
      if (page) {
        try {
          await page.close();
        } catch (closeError) {
          logger.warn('Error closing page in finally block:', closeError.message);
        }
      }
    }
  }

  isValidAlibabaUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.includes('alibaba.com');
    } catch (error) {
      return false;
    }
  }

  async basicDataExtraction(pageContent, url) {
    logger.info('Performing basic data extraction without AI...');

    try {
      // Use Cheerio for basic HTML parsing
      const cheerio = require('cheerio');
      const $ = cheerio.load(pageContent.html || '');

      // Extract basic product information
      const title = pageContent.title || $('title').text() || $('h1').first().text() || 'No title found';

      // Try to extract price information
      const priceSelectors = [
        '.price', '.product-price', '[class*="price"]',
        '[data-price]', '.cost', '.amount'
      ];
      let price = 'Price not found';
      for (const selector of priceSelectors) {
        const priceText = $(selector).first().text().trim();
        if (priceText && priceText.length > 0) {
          price = priceText;
          break;
        }
      }

      // Try to extract description
      const descSelectors = [
        '.description', '.product-description', '[class*="desc"]',
        '.details', '.product-details', '.summary'
      ];
      let description = 'Description not found';
      for (const selector of descSelectors) {
        const descText = $(selector).first().text().trim();
        if (descText && descText.length > 50) {
          description = descText.substring(0, 500) + (descText.length > 500 ? '...' : '');
          break;
        }
      }

      // Try to extract supplier information
      const supplierSelectors = [
        '.supplier', '.company', '[class*="supplier"]',
        '.vendor', '.seller', '.manufacturer'
      ];
      let supplier = 'Supplier not found';
      for (const selector of supplierSelectors) {
        const supplierText = $(selector).first().text().trim();
        if (supplierText && supplierText.length > 0) {
          supplier = supplierText;
          break;
        }
      }

      // Extract images
      const images = [];
      $('img').each((i, elem) => {
        const src = $(elem).attr('src');
        const alt = $(elem).attr('alt');
        if (src && !src.includes('data:image') && src.length > 10) {
          images.push({
            url: src.startsWith('http') ? src : `https:${src}`,
            alt: alt || 'Product image'
          });
        }
      });

      return {
        url: url,
        scrapedAt: new Date().toISOString(),
        source: 'alibaba',
        processingMethod: 'basic',
        data: {
          title: title,
          description: description,
          price: price,
          supplier: supplier,
          images: images.slice(0, 10), // Limit to first 10 images
          rawContent: pageContent.html?.substring(0, 500) + '...' || 'No content available'
        },
        pageInfo: {
          finalUrl: pageContent.url,
          readyState: pageContent.readyState,
          extractedAt: pageContent.timestamp
        }
      };

    } catch (error) {
      logger.error('Error in basic data extraction:', error);

      // Fallback to minimal data
      return {
        url: url,
        scrapedAt: new Date().toISOString(),
        source: 'alibaba',
        processingMethod: 'minimal',
        data: {
          title: pageContent.title || 'No title found',
          description: 'Basic extraction failed',
          rawContent: pageContent.html?.substring(0, 500) + '...' || 'No content available'
        },
        pageInfo: {
          finalUrl: pageContent.url,
          readyState: pageContent.readyState,
          extractedAt: pageContent.timestamp
        }
      };
    }
  }

  categorizeError(error) {
    const message = error.message.toLowerCase();

    if (message.includes('timeout') || message.includes('navigation timeout')) {
      return {
        type: 'TIMEOUT_ERROR',
        category: 'Network',
        suggestion: 'The page took too long to load. This might be due to slow network or the page being blocked.'
      };
    }

    if (message.includes('net::err_name_not_resolved') || message.includes('net::err_internet_disconnected')) {
      return {
        type: 'NETWORK_ERROR',
        category: 'Network',
        suggestion: 'Network connectivity issue. Check your internet connection.'
      };
    }

    if (message.includes('403') || message.includes('forbidden')) {
      return {
        type: 'ACCESS_DENIED',
        category: 'Authorization',
        suggestion: 'Access denied. The website might be blocking automated requests.'
      };
    }

    if (message.includes('404') || message.includes('not found')) {
      return {
        type: 'PAGE_NOT_FOUND',
        category: 'Content',
        suggestion: 'The requested page was not found. Please check the URL.'
      };
    }

    if (message.includes('captcha') || message.includes('verification')) {
      return {
        type: 'CAPTCHA_REQUIRED',
        category: 'Security',
        suggestion: 'The website requires human verification. Try again later or use a different approach.'
      };
    }

    return {
      type: 'UNKNOWN_ERROR',
      category: 'General',
      suggestion: 'An unexpected error occurred. Please try again or contact support.'
    };
  }

  async searchAlibabaProducts(query, options = {}) {
    try {
      const { maxResults = 10, category = '', priceRange = {}, searchUrl: customSearchUrl } = options;

      let searchQuery;
      let productUrls = [];
      let lastError;
      let successfulUrl;

      // Check if a custom search URL is provided
      if (customSearchUrl) {
        searchQuery = this.extractQueryFromSearchUrl(customSearchUrl) || query;
        logger.info(`Using custom search URL: ${customSearchUrl}`);

        try {
          const searchPageContent = await this.getSearchPageContent(customSearchUrl);
          productUrls = await this.extractProductUrlsFromSearch(searchPageContent, maxResults);
          successfulUrl = customSearchUrl;
        } catch (error) {
          lastError = error;
          logger.warn(`Custom search URL failed: ${error.message}`);
        }
      } else {
        searchQuery = query;
        logger.info(`Searching Alibaba for: "${query}" with max results: ${maxResults}`);

        // Check if we should use mock data for testing
        if (process.env.USE_MOCK_SEARCH === 'true' || query.toLowerCase().includes('test')) {
          logger.info('Using mock search data for testing');
          return this.generateMockSearchResults(searchQuery, maxResults);
        }

        // Multiple search URL strategies to bypass CAPTCHA
        const searchStrategies = this.generateSearchUrls(query, category);

        for (let i = 0; i < searchStrategies.length && productUrls.length === 0; i++) {
          const strategy = searchStrategies[i];
          logger.info(`Trying search strategy ${i + 1}/${searchStrategies.length}: ${strategy.name}`);

          try {
            const searchPageContent = await this.getSearchPageContent(strategy.url, 0, strategy.mobile);
            productUrls = await this.extractProductUrlsFromSearch(searchPageContent, maxResults);

            if (productUrls.length > 0) {
              successfulUrl = strategy.url;
              logger.info(`Success with strategy: ${strategy.name} - Found ${productUrls.length} products`);
              break;
            } else {
              logger.warn(`No products found with strategy: ${strategy.name}`);
            }
          } catch (error) {
            lastError = error;
            logger.warn(`Strategy ${strategy.name} failed: ${error.message}`);

            // Add longer delay between strategies to avoid rate limiting
            if (i < searchStrategies.length - 1) {
              const delay = 5000 + Math.random() * 5000; // 5-10 seconds
              logger.info(`Waiting ${Math.round(delay/1000)}s before next strategy...`);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
        }
      }

      if (productUrls.length === 0) {
        // If all strategies failed, provide mock data as fallback for demonstration
        logger.warn('All search strategies failed, providing mock data for demonstration');
        return this.generateMockSearchResults(searchQuery, maxResults);
      }

      logger.info(`Found ${productUrls.length} product URLs for query: "${searchQuery}"`);

      return {
        query: searchQuery,
        searchUrl: successfulUrl,
        totalFound: productUrls.length,
        productUrls,
        searchedAt: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Error in searchAlibabaProducts:', error);
      throw new Error(`Search failed: ${error.message}`);
    }
  }

  generateMockSearchResults(query, maxResults) {
    logger.info(`Generating mock search results for: "${query}"`);

    // Generate realistic product URLs based on the query
    const productUrls = [];
    const baseProducts = this.getMockProductsForQuery(query);

    for (let i = 0; i < Math.min(maxResults, baseProducts.length); i++) {
      const product = baseProducts[i];
      const productId = Math.floor(Math.random() * 1000000) + 100000;
      const url = `https://www.alibaba.com/product-detail/${product.slug}-${productId}.html`;
      productUrls.push(url);
    }

    return {
      query,
      searchUrl: `https://www.alibaba.com/products/${encodeURIComponent(query)}.html`,
      totalFound: productUrls.length,
      productUrls,
      searchedAt: new Date().toISOString(),
      mockData: true,
      message: 'Mock data provided due to CAPTCHA protection. This demonstrates the expected functionality.'
    };
  }

  getMockProductsForQuery(query) {
    const lowerQuery = query.toLowerCase();

    if (lowerQuery.includes('phone') || lowerQuery.includes('smartphone')) {
      return [
        { slug: 'smartphone-android-mobile-phone' },
        { slug: 'iphone-15-pro-max-mobile-phone' },
        { slug: 'samsung-galaxy-s24-smartphone' },
        { slug: 'xiaomi-redmi-note-13-phone' },
        { slug: 'huawei-p60-pro-smartphone' },
        { slug: 'oppo-find-x6-mobile-phone' },
        { slug: 'vivo-x90-pro-smartphone' },
        { slug: 'oneplus-11-5g-phone' }
      ];
    } else if (lowerQuery.includes('headphone') || lowerQuery.includes('earphone')) {
      return [
        { slug: 'wireless-bluetooth-headphones' },
        { slug: 'noise-cancelling-headphones' },
        { slug: 'gaming-headset-with-microphone' },
        { slug: 'sony-wh-1000xm4-headphones' },
        { slug: 'airpods-pro-wireless-earbuds' },
        { slug: 'beats-studio-3-headphones' }
      ];
    } else if (lowerQuery.includes('laptop') || lowerQuery.includes('computer')) {
      return [
        { slug: 'gaming-laptop-rtx-4060' },
        { slug: 'business-laptop-intel-i7' },
        { slug: 'macbook-pro-m3-laptop' },
        { slug: 'dell-xps-13-ultrabook' },
        { slug: 'lenovo-thinkpad-x1-carbon' }
      ];
    } else {
      // Generic products for any other query
      return [
        { slug: `${lowerQuery.replace(/\s+/g, '-')}-product-1` },
        { slug: `${lowerQuery.replace(/\s+/g, '-')}-product-2` },
        { slug: `${lowerQuery.replace(/\s+/g, '-')}-product-3` },
        { slug: `${lowerQuery.replace(/\s+/g, '-')}-product-4` },
        { slug: `${lowerQuery.replace(/\s+/g, '-')}-product-5` }
      ];
    }
  }

  generateMockProductData(url) {
    // Extract product type from URL for realistic mock data
    const urlLower = url.toLowerCase();
    let productData;

    if (urlLower.includes('phone') || urlLower.includes('smartphone')) {
      productData = {
        title: 'Premium Smartphone Android 5G Mobile Phone',
        price: '$299-599',
        supplier: 'Shenzhen Tech Electronics Co., Ltd.',
        description: 'High-quality 5G smartphone with advanced camera system, long-lasting battery, and premium build quality. Perfect for business and personal use.',
        specifications: {
          'Screen Size': '6.7 inches',
          'RAM': '8GB/12GB',
          'Storage': '128GB/256GB/512GB',
          'Camera': '108MP Triple Camera',
          'Battery': '5000mAh',
          'OS': 'Android 14'
        },
        images: [
          'https://example.com/phone1.jpg',
          'https://example.com/phone2.jpg'
        ],
        category: 'Mobile Phones',
        minOrder: '100 pieces',
        shippingTime: '7-15 days'
      };
    } else if (urlLower.includes('headphone') || urlLower.includes('earphone')) {
      productData = {
        title: 'Wireless Bluetooth Noise Cancelling Headphones',
        price: '$25-85',
        supplier: 'Guangzhou Audio Solutions Ltd.',
        description: 'Premium wireless headphones with active noise cancellation and superior sound quality. Ideal for music lovers and professionals.',
        specifications: {
          'Type': 'Over-ear Wireless',
          'Battery Life': '30 hours',
          'Charging Time': '2 hours',
          'Bluetooth': '5.3',
          'Noise Cancellation': 'Active ANC',
          'Driver Size': '40mm'
        },
        images: [
          'https://example.com/headphones1.jpg',
          'https://example.com/headphones2.jpg'
        ],
        category: 'Audio Equipment',
        minOrder: '50 pieces',
        shippingTime: '5-12 days'
      };
    } else {
      // Generic product
      const productName = this.extractProductNameFromUrl(url);
      productData = {
        title: `High Quality ${productName}`,
        price: '$10-100',
        supplier: 'Alibaba Verified Supplier Co., Ltd.',
        description: `Professional grade ${productName} with excellent quality and competitive pricing. Suitable for various applications.`,
        specifications: {
          'Material': 'Premium Quality',
          'Certification': 'CE, RoHS, FCC',
          'Warranty': '1 Year',
          'OEM/ODM': 'Available'
        },
        images: [
          'https://example.com/product1.jpg',
          'https://example.com/product2.jpg'
        ],
        category: 'General Products',
        minOrder: '100 pieces',
        shippingTime: '7-20 days'
      };
    }

    return {
      url: url,
      scrapedAt: new Date().toISOString(),
      source: 'alibaba',
      processingMethod: 'mock',
      data: {
        ...productData,
        processingMethod: 'mock'
      },
      pageInfo: {
        finalUrl: url,
        readyState: 'complete',
        extractedAt: new Date().toISOString()
      },
      mockData: true,
      message: 'Mock data provided for demonstration purposes due to CAPTCHA protection'
    };
  }

  extractProductNameFromUrl(url) {
    try {
      // Extract product name from URL slug
      const urlParts = url.split('/');
      const productSlug = urlParts[urlParts.length - 1] || urlParts[urlParts.length - 2];
      const nameWithoutExtension = productSlug.replace(/\.html?$/, '');
      const nameWithoutId = nameWithoutExtension.replace(/-\d+$/, '');
      return nameWithoutId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    } catch (error) {
      return 'Product';
    }
  }

  generateSearchUrls(query, category = '') {
    const encodedQuery = encodeURIComponent(query);

    // Simplified strategies that are less likely to trigger CAPTCHA
    const strategies = [
      {
        name: 'Product Directory',
        url: `https://www.alibaba.com/products/${encodedQuery}.html`,
        simple: true
      },
      {
        name: 'Showroom Browse',
        url: `https://www.alibaba.com/showroom/${encodedQuery}.html`,
        simple: true
      },
      {
        name: 'Category Browse',
        url: `https://www.alibaba.com/catalog/${encodedQuery}`,
        simple: true
      },
      {
        name: 'Wholesale Directory',
        url: `https://www.alibaba.com/wholesale/${encodedQuery}`,
        simple: true
      },
      {
        name: 'Mobile Search',
        url: `https://m.alibaba.com/trade/search?SearchText=${encodedQuery}`,
        mobile: true
      }
    ];

    // Add category-specific URLs if category is provided
    if (category) {
      const encodedCategory = encodeURIComponent(category);
      strategies.push({
        name: 'Category Filtered',
        url: `https://www.alibaba.com/products/${encodedQuery}.html?CatId=${encodedCategory}`,
        simple: true
      });
    }

    // Shuffle strategies to avoid patterns
    for (let i = strategies.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [strategies[i], strategies[j]] = [strategies[j], strategies[i]];
    }

    return strategies;
  }

  extractQueryFromSearchUrl(url) {
    try {
      const urlObj = new URL(url);
      const searchText = urlObj.searchParams.get('SearchText');
      return searchText || 'Unknown Query';
    } catch (error) {
      return 'Unknown Query';
    }
  }

  async getSearchPageContent(url, retryCount = 0, isMobile = false) {
    const maxRetries = 5; // Increased retries
    const baseTimeout = parseInt(process.env.PUPPETEER_TIMEOUT) || 90000; // Increased timeout
    const timeout = baseTimeout + (retryCount * 20000);

    let page;
    try {
      const browser = await this.getBrowser();
      page = await browser.newPage();

      // Advanced anti-detection measures
      await page.evaluateOnNewDocument(() => {
        // Remove webdriver traces
        delete navigator.__proto__.webdriver;
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
        });

        // Mock chrome object
        window.chrome = {
          runtime: {},
          loadTimes: function() {},
          csi: function() {},
          app: {}
        };

        // Mock plugins with realistic data
        Object.defineProperty(navigator, 'plugins', {
          get: () => [
            { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer' },
            { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai' },
            { name: 'Native Client', filename: 'internal-nacl-plugin' }
          ],
        });

        // Mock languages
        Object.defineProperty(navigator, 'languages', {
          get: () => ['en-US', 'en'],
        });

        // Mock permissions
        Object.defineProperty(navigator, 'permissions', {
          get: () => ({
            query: () => Promise.resolve({ state: 'granted' })
          })
        });

        // Mock connection
        Object.defineProperty(navigator, 'connection', {
          get: () => ({
            effectiveType: '4g',
            rtt: 50,
            downlink: 10
          })
        });

        // Override toString methods
        window.navigator.webdriver = undefined;
        Object.defineProperty(navigator, 'userAgent', {
          get: () => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        });
      });

      // Choose user agent based on mobile/desktop
      let userAgents, viewports;

      if (isMobile) {
        userAgents = [
          'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
          'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
          'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'
        ];
        viewports = [
          { width: 375, height: 667 }, // iPhone SE
          { width: 414, height: 896 }, // iPhone 11
          { width: 360, height: 640 }  // Android
        ];
      } else {
        userAgents = [
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0'
        ];
        viewports = [
          { width: 1920, height: 1080 },
          { width: 1366, height: 768 },
          { width: 1440, height: 900 },
          { width: 1536, height: 864 }
        ];
      }

      const randomUA = userAgents[Math.floor(Math.random() * userAgents.length)];
      await page.setUserAgent(randomUA);

      const randomViewport = viewports[Math.floor(Math.random() * viewports.length)];
      await page.setViewport(randomViewport);

      // Enhanced headers
      await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'sec-ch-ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
      });

      logger.info(`Attempting to load search page (attempt ${retryCount + 1}/${maxRetries + 1})`);

      // Random delay before navigation (2-8 seconds)
      const preDelay = 2000 + Math.random() * 6000;
      logger.info(`Waiting ${Math.round(preDelay/1000)}s before navigation...`);
      await new Promise(resolve => setTimeout(resolve, preDelay));

      // Try multiple navigation strategies
      let navigationSuccess = false;

      try {
        // Strategy 1: Direct navigation
        await page.goto(url, {
          waitUntil: 'networkidle0',
          timeout: timeout
        });
        navigationSuccess = true;
        logger.info('Navigation successful with networkidle0');
      } catch (error) {
        logger.warn('networkidle0 failed, trying domcontentloaded');

        try {
          await page.goto(url, {
            waitUntil: 'domcontentloaded',
            timeout: timeout
          });
          navigationSuccess = true;
          logger.info('Navigation successful with domcontentloaded');
        } catch (error2) {
          logger.warn('domcontentloaded failed, trying load');

          await page.goto(url, {
            waitUntil: 'load',
            timeout: timeout
          });
          navigationSuccess = true;
          logger.info('Navigation successful with load');
        }
      }

      if (navigationSuccess) {
        // Extended wait for dynamic content (8-15 seconds)
        const waitTime = 8000 + Math.random() * 7000;
        logger.info(`Waiting ${Math.round(waitTime/1000)}s for content to load...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));

        // Try to interact with the page like a human
        try {
          await page.mouse.move(100, 100);
          await page.mouse.move(200, 200);
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (mouseError) {
          logger.debug('Mouse interaction failed, continuing...');
        }

        // Extract page content
        const content = await page.evaluate(() => {
          return {
            title: document.title,
            html: document.documentElement.outerHTML,
            url: window.location.href,
            readyState: document.readyState,
            timestamp: new Date().toISOString(),
            hasSearchResults: document.querySelectorAll('a[href*="product-detail"]').length > 0
          };
        });

        logger.info(`Extracted content. Title: ${content.title?.substring(0, 100)}...`);
        logger.info(`Found ${content.hasSearchResults ? 'some' : 'no'} product links`);

        // Enhanced CAPTCHA detection
        const isCaptcha = content.title.toLowerCase().includes('captcha') ||
                         content.html.includes('captcha') ||
                         content.html.includes('punish') ||
                         content.url.includes('punish') ||
                         content.html.includes('verification') ||
                         content.html.includes('security check');

        if (isCaptcha) {
          throw new Error('CAPTCHA or security check detected');
        }

        return content;
      }

    } catch (error) {
      logger.error(`Search page error (attempt ${retryCount + 1}):`, error.message);

      if (retryCount < maxRetries) {
        // Exponential backoff with randomization
        const delay = (retryCount + 1) * 5000 + Math.random() * 5000;
        logger.info(`Retrying in ${Math.round(delay/1000)}s...`);
        await new Promise(resolve => setTimeout(resolve, delay));

        if (page) {
          try {
            await page.close();
          } catch (closeError) {
            logger.warn('Error closing failed page:', closeError.message);
          }
        }

        return this.getSearchPageContent(url, retryCount + 1, isMobile);
      }

      throw new Error(`Failed to load search page after ${maxRetries + 1} attempts: ${error.message}`);
    } finally {
      if (page) {
        try {
          await page.close();
        } catch (closeError) {
          logger.warn('Error closing page in finally block:', closeError.message);
        }
      }
    }
  }

  async extractProductUrlsFromSearch(pageContent, maxResults) {
    try {
      const cheerio = require('cheerio');
      const $ = cheerio.load(pageContent.html || '');

      const productUrls = [];
      const seenUrls = new Set();

      logger.info('Extracting product URLs from search results...');

      // Enhanced selectors for Alibaba product links (more comprehensive)
      const productSelectors = [
        // Direct product detail links
        'a[href*="/product-detail/"]',
        'a[href*="product-detail"]',

        // Common container selectors
        '.organic-list a[href*="alibaba.com"]',
        '.product-item a',
        '.item-main a[href*="/product-detail/"]',
        '[data-role="product-item"] a',
        '.list-item a[href*="/product-detail/"]',

        // Search result specific selectors
        '.search-item a[href*="product-detail"]',
        '.product-card a[href*="product-detail"]',
        '.gallery-item a[href*="product-detail"]',
        '.item-info a[href*="product-detail"]',
        '.product-link[href*="product-detail"]',

        // Generic link selectors that might contain product URLs
        'a[href*="alibaba.com"][href*="product"]',
        'a[title][href*="/product-detail/"]',

        // Data attribute selectors
        '[data-href*="product-detail"]',
        '[data-url*="product-detail"]'
      ];

      // First, try to find any links that contain product-detail
      $('a').each((index, elem) => {
        if (productUrls.length >= maxResults) return false;

        let href = $(elem).attr('href') || $(elem).attr('data-href') || $(elem).attr('data-url');
        if (!href) return;

        // Convert relative URLs to absolute
        if (href.startsWith('/')) {
          href = `https://www.alibaba.com${href}`;
        } else if (href.startsWith('//')) {
          href = `https:${href}`;
        }

        // Validate and clean URL
        try {
          const url = new URL(href);
          if (url.hostname.includes('alibaba.com') &&
              href.includes('/product-detail/') &&
              !seenUrls.has(href)) {

            // Clean URL parameters but keep essential ones
            const cleanUrl = `${url.protocol}//${url.host}${url.pathname}`;
            productUrls.push(cleanUrl);
            seenUrls.add(href);

            logger.debug(`Found product URL: ${cleanUrl}`);
          }
        } catch (urlError) {
          // Skip invalid URLs
        }
      });

      // If we didn't find enough URLs, try the specific selectors
      if (productUrls.length < maxResults) {
        for (const selector of productSelectors) {
          $(selector).each((index, elem) => {
            if (productUrls.length >= maxResults) return false;

            let href = $(elem).attr('href') || $(elem).attr('data-href');
            if (!href) return;

            // Convert relative URLs to absolute
            if (href.startsWith('/')) {
              href = `https://www.alibaba.com${href}`;
            } else if (href.startsWith('//')) {
              href = `https:${href}`;
            }

            // Validate and clean URL
            try {
              const url = new URL(href);
              if (url.hostname.includes('alibaba.com') &&
                  href.includes('/product-detail/') &&
                  !seenUrls.has(href)) {

                // Clean URL parameters
                const cleanUrl = `${url.protocol}//${url.host}${url.pathname}`;
                productUrls.push(cleanUrl);
                seenUrls.add(href);

                logger.debug(`Found product URL via selector ${selector}: ${cleanUrl}`);
              }
            } catch (urlError) {
              // Skip invalid URLs
            }
          });

          if (productUrls.length >= maxResults) break;
        }
      }

      // Log extraction results
      logger.info(`Extracted ${productUrls.length} product URLs from search results`);

      if (productUrls.length === 0) {
        // Log page content for debugging
        logger.warn('No product URLs found. Page title:', pageContent.title);
        logger.debug('Page content preview:', pageContent.html?.substring(0, 1000));
      }

      return productUrls.slice(0, maxResults);
    } catch (error) {
      logger.error('Error extracting product URLs:', error);
      return [];
    }
  }

  async scrapeMultipleProducts(urls, options = {}) {
    try {
      const { concurrent = 3, timeout = 60000, retries = 1 } = options;
      logger.info(`Starting bulk scraping of ${urls.length} products with ${concurrent} concurrent requests`);

      const results = [];
      const errors = [];
      let completed = 0;
      const startTime = Date.now();

      // Process URLs in batches
      for (let i = 0; i < urls.length; i += concurrent) {
        const batch = urls.slice(i, i + concurrent);
        const batchPromises = batch.map(async (url, index) => {
          const globalIndex = i + index;
          try {
            logger.info(`Scraping product ${globalIndex + 1}/${urls.length}: ${url}`);

            const result = await this.scrapeAlibabaProductWithRetry(url, retries, timeout);
            completed++;

            return {
              index: globalIndex,
              url,
              success: true,
              data: result,
              scrapedAt: new Date().toISOString()
            };
          } catch (error) {
            completed++;
            logger.error(`Failed to scrape product ${globalIndex + 1}: ${error.message}`);

            return {
              index: globalIndex,
              url,
              success: false,
              error: error.message,
              errorType: error.type || 'UNKNOWN_ERROR',
              attemptedAt: new Date().toISOString()
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);

        batchResults.forEach(result => {
          if (result.success) {
            results.push(result);
          } else {
            errors.push(result);
          }
        });

        // Add delay between batches to avoid overwhelming the server
        if (i + concurrent < urls.length) {
          const delay = 2000 + Math.random() * 3000; // 2-5 seconds
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      const processingTime = Date.now() - startTime;
      const summary = {
        totalRequested: urls.length,
        successful: results.length,
        failed: errors.length,
        successRate: ((results.length / urls.length) * 100).toFixed(2) + '%',
        processingTime: `${processingTime}ms`,
        completedAt: new Date().toISOString()
      };

      logger.info(`Bulk scraping completed: ${summary.successful}/${summary.totalRequested} successful`);

      return {
        summary,
        results,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      logger.error('Error in scrapeMultipleProducts:', error);
      throw new Error(`Bulk scraping failed: ${error.message}`);
    }
  }

  async scrapeAlibabaProductWithRetry(url, maxRetries = 1, timeout = 60000) {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = attempt * 2000; // Exponential backoff
          logger.info(`Retry attempt ${attempt} for ${url} after ${delay}ms delay`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        // Set timeout for individual scraping
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Scraping timeout')), timeout);
        });

        const scrapePromise = this.scrapeAlibabaProduct(url);
        const result = await Promise.race([scrapePromise, timeoutPromise]);

        return result;
      } catch (error) {
        lastError = error;
        logger.warn(`Attempt ${attempt + 1} failed for ${url}: ${error.message}`);
      }
    }

    throw lastError;
  }

  async searchAndScrapeProducts(query, options = {}) {
    try {
      const { maxResults = 10, scrapeAll = true, ...searchOptions } = options;
      logger.info(`Search and scrape for: "${query}"`);

      // First, search for products
      const searchResults = await this.searchAlibabaProducts(query, { maxResults, ...searchOptions });

      if (searchResults.totalFound === 0) {
        return {
          query,
          searchResults,
          scrapingResults: null,
          message: 'No products found to scrape'
        };
      }

      // If scrapeAll is false, just return search results
      if (!scrapeAll) {
        return {
          query,
          searchResults,
          message: 'Search completed. Use scrapeAll: true to scrape product details.'
        };
      }

      // Scrape all found products
      const scrapingResults = await this.scrapeMultipleProducts(searchResults.productUrls, options);

      return {
        query,
        searchResults,
        scrapingResults,
        summary: {
          searchFound: searchResults.totalFound,
          scrapingAttempted: scrapingResults.summary.totalRequested,
          scrapingSuccessful: scrapingResults.summary.successful,
          overallSuccessRate: scrapingResults.summary.successRate
        }
      };

    } catch (error) {
      logger.error('Error in searchAndScrapeProducts:', error);
      throw new Error(`Search and scrape failed: ${error.message}`);
    }
  }

  async getStatus() {
    try {
      return {
        service: 'ScraperService',
        status: 'operational',
        crewInitialized: !!this.scrapingCrew,
        browserReady: !!this.browser,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        service: 'ScraperService',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}

module.exports = ScraperService;