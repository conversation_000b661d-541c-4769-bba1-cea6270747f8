const Joi = require('joi');
const logger = require('../utils/logger');

const urlSchema = Joi.object({
  url: Joi.string()
    .uri({
      scheme: ['http', 'https']
    })
    .required()
    .messages({
      'string.uri': 'Please provide a valid URL',
      'any.required': 'URL is required'
    })
});

const validateUrl = (req, res, next) => {
  const { error, value } = urlSchema.validate(req.body);
  
  if (error) {
    logger.warn(`Validation error: ${error.details[0].message}`);
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      message: error.details[0].message,
      field: error.details[0].path[0]
    });
  }

  // Additional validation for Alibaba URLs
  try {
    const url = new URL(value.url);
    if (!url.hostname.includes('alibaba.com')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL',
        message: 'Please provide a valid Alibaba.com URL'
      });
    }
  } catch (urlError) {
    return res.status(400).json({
      success: false,
      error: 'Invalid URL format',
      message: 'The provided URL is not properly formatted'
    });
  }

  req.body = value;
  next();
};

module.exports = {
  validateUrl
};