const Joi = require('joi');
const logger = require('../utils/logger');

const urlSchema = Joi.object({
  url: Joi.string()
    .uri({
      scheme: ['http', 'https']
    })
    .required()
    .messages({
      'string.uri': 'Please provide a valid URL',
      'any.required': 'URL is required'
    })
});

const searchSchema = Joi.object({
  query: Joi.string()
    .min(2)
    .max(100)
    .required()
    .messages({
      'string.min': 'Search query must be at least 2 characters long',
      'string.max': 'Search query must be less than 100 characters',
      'any.required': 'Search query is required'
    }),
  maxResults: Joi.number()
    .integer()
    .min(1)
    .max(50)
    .default(10)
    .messages({
      'number.min': 'Maximum results must be at least 1',
      'number.max': 'Maximum results cannot exceed 50'
    }),
  category: Joi.string()
    .optional()
    .messages({
      'string.base': 'Category must be a string'
    }),
  priceRange: Joi.object({
    min: Joi.number().min(0).optional(),
    max: Joi.number().min(0).optional()
  }).optional()
});

const bulkUrlsSchema = Joi.object({
  urls: Joi.array()
    .items(
      Joi.string().uri({
        scheme: ['http', 'https']
      })
    )
    .min(1)
    .max(20)
    .required()
    .messages({
      'array.min': 'At least one URL is required',
      'array.max': 'Maximum 20 URLs allowed per request',
      'any.required': 'URLs array is required'
    }),
  options: Joi.object({
    concurrent: Joi.number().integer().min(1).max(5).default(3),
    timeout: Joi.number().integer().min(30000).max(300000).default(60000),
    retries: Joi.number().integer().min(0).max(3).default(1)
  }).optional()
});

const validateUrl = (req, res, next) => {
  const { error, value } = urlSchema.validate(req.body);

  if (error) {
    logger.warn(`Validation error: ${error.details[0].message}`);
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      message: error.details[0].message,
      field: error.details[0].path[0]
    });
  }

  // Additional validation for Alibaba URLs
  try {
    const url = new URL(value.url);
    if (!url.hostname.includes('alibaba.com')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL',
        message: 'Please provide a valid Alibaba.com URL'
      });
    }
  } catch (urlError) {
    return res.status(400).json({
      success: false,
      error: 'Invalid URL format',
      message: 'The provided URL is not properly formatted'
    });
  }

  req.body = value;
  next();
};

const validateSearch = (req, res, next) => {
  const { error, value } = searchSchema.validate(req.body);

  if (error) {
    logger.warn(`Search validation error: ${error.details[0].message}`);
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      message: error.details[0].message,
      field: error.details[0].path[0]
    });
  }

  req.body = value;
  next();
};

const validateBulkUrls = (req, res, next) => {
  const { error, value } = bulkUrlsSchema.validate(req.body);

  if (error) {
    logger.warn(`Bulk URLs validation error: ${error.details[0].message}`);
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      message: error.details[0].message,
      field: error.details[0].path[0]
    });
  }

  // Validate each URL is from Alibaba
  const invalidUrls = [];
  value.urls.forEach((url, index) => {
    try {
      const urlObj = new URL(url);
      if (!urlObj.hostname.includes('alibaba.com')) {
        invalidUrls.push({ index, url, reason: 'Not an Alibaba.com URL' });
      }
    } catch (urlError) {
      invalidUrls.push({ index, url, reason: 'Invalid URL format' });
    }
  });

  if (invalidUrls.length > 0) {
    return res.status(400).json({
      success: false,
      error: 'Invalid URLs found',
      message: 'Some URLs are not valid Alibaba.com URLs',
      invalidUrls
    });
  }

  req.body = value;
  next();
};

module.exports = {
  validateUrl,
  validateSearch,
  validateBulkUrls
};