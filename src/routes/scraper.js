const express = require('express');
const router = express.Router();
const { validateUrl, validateSearch, validateBulkUrls } = require('../middleware/validation');
const ScraperService = require('../services/scraperService');
const logger = require('../utils/logger');

const scraperService = new ScraperService();

/**
 * POST /api/scraper/alibaba
 * Scrape Alibaba product page
 */
router.post('/alibaba', validateUrl, async (req, res) => {
  try {
    const { url } = req.body;

    logger.info(`Starting scrape for URL: ${url}`);

    const startTime = Date.now();
    const result = await scraperService.scrapeAlibabaProduct(url);
    const duration = Date.now() - startTime;

    logger.info(`Scrape completed in ${duration}ms`);

    res.status(200).json({
      success: true,
      data: result,
      metadata: {
        url,
        scrapedAt: new Date().toISOString(),
        processingTime: `${duration}ms`
      }
    });

  } catch (error) {
    logger.error('Scraping error:', error);

    // Determine appropriate HTTP status code based on error type
    let statusCode = 500;
    if (error.type === 'PAGE_NOT_FOUND') {
      statusCode = 404;
    } else if (error.type === 'ACCESS_DENIED') {
      statusCode = 403;
    } else if (error.type === 'TIMEOUT_ERROR' || error.type === 'NETWORK_ERROR') {
      statusCode = 408; // Request Timeout
    }

    res.status(statusCode).json({
      success: false,
      error: 'Failed to scrape product data',
      message: error.message,
      errorType: error.type || 'UNKNOWN_ERROR',
      category: error.category || 'General',
      suggestion: error.suggestion || 'Please try again or contact support.',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/scraper/search
 * Search for products on Alibaba
 */
router.post('/search', validateSearch, async (req, res) => {
  try {
    const { query, maxResults, category, priceRange } = req.body;

    logger.info(`Starting search for query: "${query}"`);

    const startTime = Date.now();
    const result = await scraperService.searchAlibabaProducts(query, {
      maxResults,
      category,
      priceRange
    });
    const duration = Date.now() - startTime;

    logger.info(`Search completed in ${duration}ms, found ${result.totalFound} products`);

    res.status(200).json({
      success: true,
      data: result,
      metadata: {
        query,
        searchedAt: new Date().toISOString(),
        processingTime: `${duration}ms`
      }
    });

  } catch (error) {
    logger.error('Search error:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to search products',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/scraper/bulk
 * Scrape multiple Alibaba product URLs
 */
router.post('/bulk', validateBulkUrls, async (req, res) => {
  try {
    const { urls, options = {} } = req.body;

    logger.info(`Starting bulk scraping of ${urls.length} products`);

    const startTime = Date.now();
    const result = await scraperService.scrapeMultipleProducts(urls, options);
    const duration = Date.now() - startTime;

    logger.info(`Bulk scraping completed in ${duration}ms: ${result.summary.successful}/${result.summary.totalRequested} successful`);

    res.status(200).json({
      success: true,
      data: result,
      metadata: {
        totalUrls: urls.length,
        scrapedAt: new Date().toISOString(),
        processingTime: `${duration}ms`
      }
    });

  } catch (error) {
    logger.error('Bulk scraping error:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to scrape multiple products',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/scraper/search-and-scrape
 * Search for products and scrape all results
 */
router.post('/search-and-scrape', validateSearch, async (req, res) => {
  try {
    const { query, maxResults, category, priceRange, scrapeAll = true, ...options } = req.body;

    logger.info(`Starting search and scrape for query: "${query}"`);

    const startTime = Date.now();
    const result = await scraperService.searchAndScrapeProducts(query, {
      maxResults,
      category,
      priceRange,
      scrapeAll,
      ...options
    });
    const duration = Date.now() - startTime;

    logger.info(`Search and scrape completed in ${duration}ms`);

    res.status(200).json({
      success: true,
      data: result,
      metadata: {
        query,
        processedAt: new Date().toISOString(),
        processingTime: `${duration}ms`
      }
    });

  } catch (error) {
    logger.error('Search and scrape error:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to search and scrape products',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/scraper/status
 * Get scraper service status
 */
router.get('/status', async (req, res) => {
  try {
    const status = await scraperService.getStatus();
    res.status(200).json({
      success: true,
      status
    });
  } catch (error) {
    logger.error('Status check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get service status'
    });
  }
});

module.exports = router;