# Server Configuration
PORT=3000
NODE_ENV=development

DB_HOST=localhost
DB_PORT=3306
DB_NAME=scraping_bot
DB_USER=root
DB_PASSWORD=Root@123

# OpenAI API Configuration (required for CrewAI AI mode)
# CrewAI uses OpenAI's GPT models (gpt-3.5-turbo) for AI processing
# Get your API key from: https://platform.openai.com/account/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# Puppeteer Configuration
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=60000

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes in milliseconds
RATE_LIMIT_MAX_REQUESTS=100  # Maximum requests per window

# Logging Configuration
LOG_LEVEL=info  # Options: error, warn, info, debug

# Security Configuration (optional)
# HELMET_CONTENT_SECURITY_POLICY=false
# CORS_ORIGIN=*

# Performance Configuration (optional)
# MAX_CONCURRENT_REQUESTS=5
# BROWSER_POOL_SIZE=3
